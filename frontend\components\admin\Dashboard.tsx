import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAdminAuth } from '../../contexts/AdminAuthContext';
import Icon from './Icon';
import { apiClient } from '../../utils/axiosClient';

interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  lockedUsers: number;
  totalAdmins: number;
  totalBuilds: number;
  systemHealth?: 'OK' | 'WARN' | 'ERROR';
  uptime?: string;
  recentFailedLogins?: number;
  userGrowth?: number;
  serverLoad?: number;
}

interface StatCardProps {
  title: string;
  value: string | number;
  icon: string;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple';
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  icon, 
  change, 
  changeType = 'neutral',
  color = 'blue' 
}) => {
  const colorClasses = {
    blue: 'bg-blue-500 text-blue-100',
    green: 'bg-green-500 text-green-100',
    red: 'bg-red-500 text-red-100',
    yellow: 'bg-yellow-500 text-yellow-100',
    purple: 'bg-purple-500 text-purple-100',
  };

  const changeClasses = {
    positive: 'text-green-600 bg-green-100',
    negative: 'text-red-600 bg-red-100',
    neutral: 'text-gray-600 bg-gray-100',
  };

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-2">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mb-1">{value}</p>
          {change && (
            <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${changeClasses[changeType]}`}>
              <Icon 
                name={changeType === 'positive' ? 'trending-up' : changeType === 'negative' ? 'trending-down' : 'minus'} 
                size={12} 
                className="mr-1" 
              />
              {change}
            </div>
          )}
        </div>
        <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${colorClasses[color]}`}>
          <Icon name={icon as any} size={24} />
        </div>
      </div>
    </div>
  );
};

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const router = useRouter();
  const { admin, checkPermission } = useAdminAuth();

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await apiClient.backend.get('/api/dashboard/stats');
      setStats(response.data);
    } catch (err: any) {
      console.error('Dashboard fetch error:', err);
      
      // Set demo data for development/testing
      const mockStats = {
        totalUsers: 1247,
        activeUsers: 892,
        lockedUsers: 23,
        totalAdmins: 8,
        totalBuilds: 156,
        systemHealth: 'OK' as const,
        uptime: '15d 8h 32m',
        recentFailedLogins: 5,
        userGrowth: 12.5,
        serverLoad: 65,
      };
      
      setStats(mockStats);
      
      // Set appropriate error message based on error type
      if (err.response?.status === 403) {
        setError('Access denied - insufficient permissions. Using demo data.');
      } else if (err.response?.status === 401) {
        setError('Authentication required. Please log in again.');
      } else if (err.code === 'NETWORK_ERROR' || !err.response) {
        setError('Network error - unable to connect to server. Using demo data.');
      } else {
        setError(`API Error (${err.response?.status || 'Unknown'}): ${err.response?.data?.message || err.message}. Using demo data.`);
      }
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Loading Header */}
        <div className="flex justify-between items-center">
          <div>
            <div className="h-8 bg-gray-300 rounded-lg w-48 mb-2 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-64 animate-pulse"></div>
          </div>
          <div className="h-10 bg-gray-300 rounded-lg w-24 animate-pulse"></div>
        </div>

        {/* Loading Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-24 mb-3"></div>
                <div className="h-8 bg-gray-300 rounded w-16 mb-2"></div>
                <div className="h-6 bg-gray-200 rounded w-20"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Loading Chart Areas */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(2)].map((_, i) => (
            <div key={i} className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
              <div className="animate-pulse">
                <div className="h-6 bg-gray-300 rounded w-32 mb-4"></div>
                <div className="h-64 bg-gray-200 rounded-xl"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Welcome back, {admin?.username}! Here's what's happening with your system.
          </p>
        </div>
        <button
          onClick={fetchDashboardStats}
          disabled={loading}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors disabled:opacity-50"
        >
          <Icon name="refresh" size={16} className="mr-2" />
          Refresh
        </button>
      </div>

      {/* Error Alert */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4">
          <div className="flex items-center">
            <Icon name="alert-circle" size={20} className="text-red-500 mr-3" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-red-800">Connection Issue</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
            <button
              onClick={() => setError('')}
              className="text-red-400 hover:text-red-500 transition-colors"
            >
              <Icon name="close" size={16} />
            </button>
          </div>
        </div>
      )}

      {stats && (
        <>
          {/* Stats Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title="Total Users"
              value={stats?.totalUsers?.toLocaleString() || '0'}
              icon="users"
              change={stats?.userGrowth ? `+${stats.userGrowth}%` : undefined}
              changeType="positive"
              color="blue"
            />
            <StatCard
              title="Active Users"
              value={stats?.activeUsers?.toLocaleString() || '0'}
              icon="user-check"
              change="+5.2%"
              changeType="positive"
              color="green"
            />
            <StatCard
              title="Locked Users"
              value={stats?.lockedUsers || 0}
              icon="lock"
              change={stats?.lockedUsers && stats.lockedUsers > 20 ? 'High' : 'Normal'}
              changeType={stats?.lockedUsers && stats.lockedUsers > 20 ? 'negative' : 'neutral'}
              color="red"
            />
            <StatCard
              title="System Health"
              value={stats?.systemHealth || 'OK'}
              icon="shield"
              change={stats?.serverLoad ? `${stats.serverLoad}% load` : 'Unknown'}
              changeType={stats?.serverLoad && stats.serverLoad > 80 ? 'negative' : 'positive'}
              color={stats?.systemHealth === 'OK' ? 'green' : 'yellow'}
            />
          </div>

          {/* Charts and Quick Actions */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Quick Actions */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button
                  onClick={() => router.push('/admin/users')}
                  className="w-full flex items-center justify-between p-3 text-left text-gray-700 hover:bg-gray-50 rounded-xl transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <Icon name="users" size={20} className="text-indigo-600" />
                    <span className="font-medium">Manage Users</span>
                  </div>
                  <Icon name="chevron-right" size={16} className="text-gray-400" />
                </button>
                
                <button
                  onClick={() => router.push('/admin/security')}
                  className="w-full flex items-center justify-between p-3 text-left text-gray-700 hover:bg-gray-50 rounded-xl transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <Icon name="security" size={20} className="text-green-600" />
                    <span className="font-medium">Security Settings</span>
                  </div>
                  <Icon name="chevron-right" size={16} className="text-gray-400" />
                </button>
                
                <button
                  onClick={() => router.push('/admin/system-logs')}
                  className="w-full flex items-center justify-between p-3 text-left text-gray-700 hover:bg-gray-50 rounded-xl transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <Icon name="view" size={20} className="text-purple-600" />
                    <span className="font-medium">View System Logs</span>
                  </div>
                  <Icon name="chevron-right" size={16} className="text-gray-400" />
                </button>

                {checkPermission && checkPermission('superadmin') && (
                  <button
                    onClick={() => router.push('/admin/superuser')}
                    className="w-full flex items-center justify-between p-3 text-left text-gray-700 hover:bg-gray-50 rounded-xl transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <Icon name="settings" size={20} className="text-red-600" />
                      <span className="font-medium">Super Admin</span>
                    </div>
                    <Icon name="chevron-right" size={16} className="text-gray-400" />
                  </button>
                )}
              </div>
            </div>

            {/* System Info */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">System Information</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Uptime</span>
                  <span className="font-medium text-gray-900">{stats?.uptime || 'N/A'}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Total Builds</span>
                  <span className="font-medium text-gray-900">{stats?.totalBuilds || 0}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Admins</span>
                  <span className="font-medium text-gray-900">{stats?.totalAdmins || 0}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Failed Logins (24h)</span>
                  <span className={`font-medium ${
                    (stats?.recentFailedLogins || 0) > 10 ? 'text-red-600' : 'text-gray-900'
                  }`}>
                    {stats?.recentFailedLogins || 0}
                  </span>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">12 new user registrations</p>
                    <p className="text-xs text-gray-500">2 hours ago</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">System backup completed</p>
                    <p className="text-xs text-gray-500">4 hours ago</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">Security scan finished</p>
                    <p className="text-xs text-gray-500">6 hours ago</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">Failed login attempts detected</p>
                    <p className="text-xs text-gray-500">8 hours ago</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Empty State */}
      {!loading && !stats && (
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-12 text-center">
          <Icon name="alert-circle" size={48} className="text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Data Available</h3>
          <p className="text-gray-600 mb-6">Unable to load dashboard statistics.</p>
          <button
            onClick={fetchDashboardStats}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
          >
            Try Again
          </button>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
