import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import UserModel from '../../models/User';
import crypto from 'crypto';
import { logSecurityEvent, logLoginAttempt, logAccountLockout } from '../../utils/security-logger';
import { generateCsrfToken } from '../../utils/csrf-protection';

/**
 * User login controller - handles secure authentication with device verification
 * POST /api/auth/user/login
 */
export async function userLoginController(req: Request, res: Response): Promise<void> {
  try {
    // First check if this is a mobile device request
    const userAgent = req.get('User-Agent') || '';
    const headerDeviceFingerprint = req.headers['x-device-fingerprint'];
    
    // Block web browser access (only check for device fingerprint, not BLE)
    const isWebBrowser = /Mozilla|Chrome|Safari|Firefox|Edge|Opera/i.test(userAgent);
    if (isWebBrowser && !headerDeviceFingerprint) {
      logSecurityEvent({
        eventType: 'failed_login',
        userType: 'user',
        username: 'unknown',
        ipAddress: req.ip,
        userAgent: userAgent,
        success: false,
        reason: 'Web access blocked for user login',
        details: { 
          hasDeviceFingerprint: !!headerDeviceFingerprint
        }
      });
      
      res.status(403).json({
        error: 'Web access denied - Please use the mobile application',
        code: 'WEB_ACCESS_DENIED',
        message: 'User authentication is only available through the CCALC mobile application'
      });
      return;
    }

    const { username, expression, deviceFingerprint: bodyDeviceFingerprint } = req.body as {
      username: string;
      expression: string;
      deviceFingerprint: string;
    };

    const deviceFingerprint = bodyDeviceFingerprint || headerDeviceFingerprint;
    
    // Ensure deviceFingerprint is a string (handle header array case)
    const fingerprintString = Array.isArray(deviceFingerprint) ? deviceFingerprint[0] : deviceFingerprint;
    
    if (!username || !expression || !fingerprintString) {
      res.status(400).json({ error: 'Missing required fields' });
      return;
    }

    // Rate limiting check could be added here
    const ipAddress = req.ip || req.socket.remoteAddress;
    
    const user = await UserModel.findOne({ username });
    
    if (!user) {
      // Use consistent timing to prevent username enumeration attacks
      await bcrypt.compare('dummy', '$2a$10$invalidhashfordummycomparison');
      res.status(401).json({ error: 'Authentication failed' });
      return;
    }

    if (user.status !== 'active' && user.status !== 'pending_device_registration') {
      res.status(401).json({ error: 'Account is not active' });
      return;
    }

    // Check account lock status
    if (user.lockUntil && user.lockUntil > new Date()) {
      res.status(401).json({ 
        error: 'Account is temporarily locked due to too many failed attempts',
        lockedUntil: user.lockUntil
      });
      return;
    }    // Verify expression (password equivalent)
    const isExpressionValid = await bcrypt.compare(expression, user.expressionHash);
    
    if (!isExpressionValid) {
      // Increment failed login attempts
      user.failedLoginAttempts = (user.failedLoginAttempts || 0) + 1;
      // Lock account after too many failed attempts
      if (user.failedLoginAttempts >= 5) {
        user.status = 'locked';
        // Set lockout for 30 minutes
        user.lockUntil = new Date(Date.now() + 30 * 60 * 1000);        // Log account lockout
        logAccountLockout(
          username,
          'user',
          req,
          (user as any)._id.toString()
        );
      }
      // Append failed login attempt to loginHistory
      if (!user.loginHistory) user.loginHistory = [];
      user.loginHistory.push({
        timestamp: new Date(),
        ipAddress: ipAddress || 'unknown',
        userAgent: req.headers['user-agent'] || 'unknown',
        deviceFingerprint: fingerprintString ? (crypto.createHash('sha256').update(fingerprintString).digest('hex').substring(0, 16)) : '',
        success: false,
        failureReason: 'Invalid expression'
      });
      await user.save();      // Log failed login attempt
      logLoginAttempt(
        username,
        'user',
        false,
        req,
        (user as any)._id.toString(),
        'Invalid expression',
        { attemptCount: user.failedLoginAttempts }
      );
      res.status(401).json({ error: 'Authentication failed' });
      return;
    }    // Verify device fingerprint or register it for first login
    const fingerprintHash = crypto.createHash('sha256')
      .update(fingerprintString)
      .digest('hex');

    // Check if this is first login (device registration)
    const isFirstLogin = user.status === 'pending_device_registration' || 
                         !user.deviceFingerprintHash;

    if (isFirstLogin) {
      // First login - register device fingerprint
      console.log(`First login device registration for user: ${user.username}`);
      
      user.deviceFingerprintHash = fingerprintHash;
      user.status = 'active';
      user.deviceMetadata = {
        registeredAt: new Date(),
        model: req.headers['x-device-model'] as string || 'Unknown',
        os: req.headers['x-device-os'] as string || 'Unknown'
      };
      
      // Log device registration
      logSecurityEvent({
        eventType: 'device_registration',
        userType: 'user',
        username: user.username,
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'] || 'unknown',
        success: true,
        reason: 'First login device registration',
        details: { 
          deviceModel: user.deviceMetadata.model,
          deviceOS: user.deviceMetadata.os,
          registrationTime: user.deviceMetadata.registeredAt
        }
      });
      
    } else {
      // Subsequent login - verify device fingerprint
      if (user.deviceFingerprintHash !== fingerprintHash) {
        // Log unrecognized device attempt for security audit
        console.warn(`Unrecognized device attempt for user: ${user.username}, IP: ${ipAddress}`);
        logLoginAttempt(
          username,
          'user',
          false,
          req,
          (user as any)._id.toString(),
          'Unrecognized device'
        );
        
        res.status(401).json({ error: 'Unrecognized device' });
        return;
      }
    }    // Reset failed attempts and update last login
    user.failedLoginAttempts = 0;
    user.lastLoginAt = new Date();
    user.lockUntil = undefined;
    
    // Store login info for audit trail
    const loginInfo = {
      timestamp: new Date(),
      ipAddress: ipAddress || 'unknown',
      userAgent: req.headers['user-agent'] || 'unknown',
      deviceFingerprint: fingerprintHash.substring(0, 16), // Store partial hash for reference
      success: true
    };
    // Append successful login to loginHistory
    if (!user.loginHistory) user.loginHistory = [];
    user.loginHistory.push(loginInfo);
    
    await user.save();    // Generate JWT token with appropriate claims
    const tokenPayload = {
      userId: (user as any)._id,
      username: user.username,
      type: 'user',
      fingerprint: fingerprintHash.substring(0, 16)
    };
    
    const tokenOptions = {
      expiresIn: process.env.USER_TOKEN_EXPIRY || '24h'
    };
      const token = jwt.sign(
      tokenPayload,
      process.env.JWT_SECRET || 'fallback-secret',
      tokenOptions as any
    );

    // Generate CSRF token for authenticated requests
    const csrfToken = generateCsrfToken((user as any)._id.toString());

    res.status(200).json({
      token,
      csrfToken,
      isFirstLogin,
      user: {
        id: (user as any)._id,
        username: user.username,
        displayName: user.profile?.displayName,
        status: user.status,
        deviceRegistered: !isFirstLogin
      }
    });

  } catch (error) {
    console.error('User login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Generate device registration challenge for a user
 * POST /api/auth/user/device-challenge
 */
export async function deviceChallengeController(req: Request, res: Response): Promise<void> {
  try {
    const { username } = req.body;
    
    if (!username) {
      res.status(400).json({ error: 'Username is required' });
      return;
    }
    
    // Check if user exists
    const user = await UserModel.findOne({ username });
    
    // Generate a challenge regardless of whether user exists (to prevent enumeration)
    const challenge = crypto.randomBytes(32).toString('hex');
    
    if (!user) {
      // Still return a challenge but with a delay to prevent timing attacks
      await new Promise(resolve => setTimeout(resolve, 500));
      res.status(200).json({ challenge });
      return;
    }
    
    // For an actual user, we could store this challenge temporarily
    // with a short TTL to verify the device registration
    
    res.status(200).json({
      challenge,
      requiresReset: user.status === 'locked' || !user.deviceFingerprintHash
    });
    
  } catch (error) {
    console.error('Device challenge error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
