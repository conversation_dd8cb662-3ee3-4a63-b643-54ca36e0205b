# iPhone Testing Guide for CCALC

## Prerequisites
1. Your iPhone and Windows PC must be on the same WiFi network
2. Backend must be running on Windows (port 3000)
3. Expo Go app installed on your iPhone

## Setup Instructions

### 1. Configure Firewall (Administrator Required)
Run the following script as Administrator:
```
d:/Development/Freelance/HT/CCALC/scripts/configure-firewall-for-iphone.bat
```

### 2. Verify Backend Connection
From your iPhone, open Safari and navigate to:
```
http://**************:3000/health
```
You should see a JSON response indicating the backend is healthy.

### 3. Start Expo App
On your Windows PC, run:
```
cd d:/Development/Freelance/HT/CCALC/app
node expo-start.js
```

### 4. Connect with iPhone
1. Install "Expo Go" app from the App Store
2. Scan the QR code shown in the terminal
3. The app should open in Expo Go

## Expected Experience
- The app starts with a calculator interface (similar to iOS calculator)
- To authenticate, enter a specific mathematical expression
- After authentication, you'll be taken to the chat interface

## Troubleshooting
- If your iPhone can't connect, make sure both devices are on the same WiFi network
- Check that the firewall is properly configured to allow connections
- Verify the backend is running by checking the health endpoint in Safari

## Notes
- This is a test build running in Expo Go
- The authentication uses mathematical expressions instead of real BLE earbuds
- The backend API is running on your Windows machine at **************:3000
