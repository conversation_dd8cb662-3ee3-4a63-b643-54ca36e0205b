#!/usr/bin/env node

/**
 * <PERSON>ript to switch between different app modes for testing
 * Usage: node switch-app-mode.js [expo-go|full|minimal]
 */

const fs = require('fs');
const path = require('path');

const mode = process.argv[2] || 'expo-go';

const appFiles = {
  'expo-go': 'src/App-ExpoGo.tsx',
  'full': 'src/App.tsx',
  'minimal': 'src/App-minimal.tsx'
};

const appJsContent = {
  'expo-go': `import { registerRootComponent } from 'expo';
import App from './src/App-ExpoGo';

registerRootComponent(App);`,
  
  'full': `import { registerRootComponent } from 'expo';
import App from './src/App';

registerRootComponent(App);`,
  
  'minimal': `import { registerRootComponent } from 'expo';
import App from './src/App-minimal';

registerRootComponent(App);`
};

function switchAppMode(mode) {
  if (!appFiles[mode]) {
    console.error(`❌ Invalid mode: ${mode}`);
    console.log('Available modes: expo-go, full, minimal');
    process.exit(1);
  }

  const appFile = appFiles[mode];
  const appPath = path.join(__dirname, appFile);

  // Check if the target app file exists
  if (!fs.existsSync(appPath)) {
    console.error(`❌ App file not found: ${appFile}`);
    process.exit(1);
  }

  // Update App.js to import the correct app
  const appJsPath = path.join(__dirname, 'App.js');
  fs.writeFileSync(appJsPath, appJsContent[mode]);

  console.log(`✅ Switched to ${mode} mode`);
  console.log(`📱 App.js now imports: ${appFile}`);
  
  if (mode === 'expo-go') {
    console.log(`
🔧 Expo Go Mode Active
- Simplified calculator interface
- Mock implementations for native modules
- No BLE or advanced crypto features
- Perfect for quick testing in Expo Go

To test:
1. npm start
2. Scan QR code with Expo Go app
3. Try expression: 12+5*7-3*2+8/4
`);
  } else if (mode === 'full') {
    console.log(`
🚀 Full App Mode Active
- Complete calculator and chat functionality
- Real native module implementations
- BLE authentication support
- Requires development build or physical device

To test:
1. npx expo run:ios (for development build)
2. Or build and install on device
`);
  } else if (mode === 'minimal') {
    console.log(`
🔍 Minimal Debug Mode Active
- Basic app structure only
- Useful for debugging build issues
- No complex components loaded
`);
  }
}

switchAppMode(mode);
