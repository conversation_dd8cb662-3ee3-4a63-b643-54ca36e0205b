/**
 * Expo Go Compatible App Component
 * Simplified version for testing in Expo Go with fallbacks for native modules
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

// Simple Calculator Component for Expo Go testing
const SimpleCalculator: React.FC<{ onAuthSuccess: () => void }> = ({ onAuthSuccess }) => {
  const [display, setDisplay] = useState('0');
  const [expression, setExpression] = useState('');

  const handlePress = (value: string) => {
    if (value === '=') {
      try {
        // Check for authentication expression
        const fullExpression = expression || display;
        console.log('Expression entered:', fullExpression);
        
        // Test expression: "12+5*7-3*2+8/4" = 45
        if (fullExpression === '12+5*7-3*2+8/4' || fullExpression === '45') {
          Alert.alert(
            'Authentication Success!',
            'Calculator authentication successful. In a real app, this would transition to the chat interface.',
            [
              {
                text: 'Continue to Chat',
                onPress: onAuthSuccess,
              },
            ]
          );
          return;
        }

        // Regular calculation
        const result = eval(fullExpression);
        setDisplay(result.toString());
        setExpression('');
      } catch (error) {
        setDisplay('Error');
        setExpression('');
      }
    } else if (value === 'C') {
      setDisplay('0');
      setExpression('');
    } else if (value === '⌫') {
      if (display.length > 1) {
        setDisplay(display.slice(0, -1));
      } else {
        setDisplay('0');
      }
    } else {
      if (display === '0' && value !== '.') {
        setDisplay(value);
        setExpression(value);
      } else {
        setDisplay(display + value);
        setExpression(expression + value);
      }
    }
  };

  const buttons = [
    ['C', '⌫', '%', '÷'],
    ['7', '8', '9', '×'],
    ['4', '5', '6', '-'],
    ['1', '2', '3', '+'],
    ['0', '.', '='],
  ];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#f8f9fa" />
      
      <View style={styles.header}>
        <Text style={styles.title}>CCALC</Text>
        <Text style={styles.subtitle}>Calculator Mode</Text>
      </View>

      <View style={styles.displayContainer}>
        <Text style={styles.display}>{display}</Text>
        <Text style={styles.hint}>
          Try: 12+5*7-3*2+8/4 for authentication
        </Text>
      </View>

      <View style={styles.buttonContainer}>
        {buttons.map((row, rowIndex) => (
          <View key={rowIndex} style={styles.buttonRow}>
            {row.map((button) => (
              <TouchableOpacity
                key={button}
                style={[
                  styles.button,
                  button === '=' && styles.equalsButton,
                  button === '0' && styles.zeroButton,
                ]}
                onPress={() => handlePress(button)}
              >
                <Text
                  style={[
                    styles.buttonText,
                    button === '=' && styles.equalsButtonText,
                  ]}
                >
                  {button}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        ))}
      </View>
    </SafeAreaView>
  );
};

// Simple Chat Component for demonstration
const SimpleChat: React.FC<{ onBack: () => void }> = ({ onBack }) => {
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#f8f9fa" />
      
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Secure Chat</Text>
      </View>

      <View style={styles.chatContainer}>
        <Text style={styles.chatTitle}>🔒 Secure Communication</Text>
        <Text style={styles.chatDescription}>
          This is a demonstration of the secure chat interface.
          In the full app, this would include:
        </Text>
        
        <View style={styles.featureList}>
          <Text style={styles.feature}>• End-to-end encrypted messaging</Text>
          <Text style={styles.feature}>• Superuser communication model</Text>
          <Text style={styles.feature}>• BLE-authenticated voice calls</Text>
          <Text style={styles.feature}>• Voice modulation for anonymity</Text>
          <Text style={styles.feature}>• Media attachment support</Text>
          <Text style={styles.feature}>• Device fingerprinting</Text>
        </View>

        <Text style={styles.note}>
          Note: This is a simplified version for Expo Go testing.
          The full native app includes all security features.
        </Text>
      </View>
    </SafeAreaView>
  );
};

// Main App Component
export default function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const handleAuthSuccess = () => {
    setIsAuthenticated(true);
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      {isAuthenticated ? (
        <SimpleChat onBack={handleLogout} />
      ) : (
        <SimpleCalculator onAuthSuccess={handleAuthSuccess} />
      )}
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#212529',
  },
  subtitle: {
    fontSize: 14,
    color: '#6c757d',
    marginTop: 4,
  },
  displayContainer: {
    padding: 20,
    alignItems: 'flex-end',
  },
  display: {
    fontSize: 48,
    fontWeight: '300',
    color: '#212529',
    marginBottom: 8,
  },
  hint: {
    fontSize: 12,
    color: '#6c757d',
    fontStyle: 'italic',
  },
  buttonContainer: {
    flex: 1,
    padding: 20,
  },
  buttonRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  button: {
    flex: 1,
    height: 60,
    backgroundColor: '#ffffff',
    marginHorizontal: 6,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  equalsButton: {
    backgroundColor: '#007bff',
  },
  zeroButton: {
    flex: 2,
  },
  buttonText: {
    fontSize: 24,
    fontWeight: '500',
    color: '#212529',
  },
  equalsButtonText: {
    color: '#ffffff',
  },
  backButton: {
    position: 'absolute',
    left: 20,
    top: 20,
  },
  backButtonText: {
    fontSize: 16,
    color: '#007bff',
  },
  chatContainer: {
    flex: 1,
    padding: 20,
  },
  chatTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#212529',
    marginBottom: 16,
    textAlign: 'center',
  },
  chatDescription: {
    fontSize: 16,
    color: '#495057',
    marginBottom: 20,
    lineHeight: 24,
  },
  featureList: {
    marginBottom: 20,
  },
  feature: {
    fontSize: 14,
    color: '#495057',
    marginBottom: 8,
    lineHeight: 20,
  },
  note: {
    fontSize: 12,
    color: '#6c757d',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 20,
  },
});
