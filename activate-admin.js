// Activate admin account script
const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect('*************************************************************')
  .then(async () => {
    console.log('Connected to MongoDB');
    
    // Import Admin model
    const Admin = mongoose.model('Admin', new mongoose.Schema({
      username: String,
      email: String,
      password: String,
      isActive: Boolean,
      role: String,
      ppkEnabled: Boolean,
      authMethod: String,
      publicKey: String,
      privateKey: String,
      ppkFingerprint: String
    }));

    try {
      // Find admin user
      const admin = await Admin.findOne({ username: 'admin' });
      
      if (!admin) {
        console.log('❌ Admin user not found');
        process.exit(1);
      }

      console.log('📋 Current admin status:');
      console.log('  Username:', admin.username);
      console.log('  Email:', admin.email);
      console.log('  Active:', admin.isActive);
      console.log('  Role:', admin.role);
      console.log('  PPK Enabled:', admin.ppkEnabled);
      console.log('  Auth Method:', admin.authMethod);

      if (admin.isActive) {
        console.log('✅ Admin account is already active');
      } else {
        // Activate the admin account
        await Admin.updateOne(
          { username: 'admin' },
          { 
            isActive: true,
            role: 'admin'
          }
        );
        console.log('✅ Admin account has been activated');
      }

      console.log('\n🔑 Admin credentials:');
      console.log('  Username: admin');
      console.log('  Password: admin123');
      console.log('  Login URL: http://localhost:3005/admin/login');
      
    } catch (error) {
      console.error('❌ Error:', error.message);
    } finally {
      await mongoose.disconnect();
      console.log('Disconnected from MongoDB');
    }
  })
  .catch(error => {
    console.error('❌ MongoDB connection error:', error.message);
    process.exit(1);
  });
