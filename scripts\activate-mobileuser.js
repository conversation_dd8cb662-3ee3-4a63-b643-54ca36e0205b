const mongoose = require('mongoose');

// MongoDB connection string
const mongoUri = '*************************************************************';

// User schema
const UserSchema = new mongoose.Schema({
  username: String,
  status: String,
  deviceFingerprintHash: String,
  deviceMetadata: Object,
}, { timestamps: true });

const User = mongoose.model('User', UserSchema);

async function activateMobileUser() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB successfully');

    // Activate the mobileuser account
    const result = await User.updateOne(
      { username: 'mobileuser' },
      { 
        $set: { 
          status: 'active',
          deviceFingerprintHash: 'mobile_device_temp_hash',
          deviceMetadata: {
            platform: 'ios',
            deviceType: 'mobile',
            registeredAt: new Date()
          }
        }
      }
    );

    if (result.matchedCount === 0) {
      console.log('❌ User "mobileuser" not found');
    } else {
      console.log('✅ User "mobileuser" activated successfully');
      
      // Verify the update
      const user = await User.findOne({ username: 'mobileuser' });
      if (user) {
        console.log('\n📱 ACTIVATED USER STATUS:');
        console.log(`Username: ${user.username}`);
        console.log(`Status: ${user.status}`);
        console.log(`Device Fingerprint: ${user.deviceFingerprintHash ? 'Registered' : 'None'}`);
        console.log(`Created: ${user.createdAt}`);
        console.log(`Updated: ${user.updatedAt}`);
        
        console.log('\n🎉 User is now ready for chat functionality!');
        console.log('The mobile app should now be able to load chat users.');
      }
    }

  } catch (error) {
    console.error('Error activating user:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDatabase connection closed');
  }
}

activateMobileUser();
