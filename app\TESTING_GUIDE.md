# CCALC iOS App Testing Guide

## 🚨 Current Issue Resolution

The errors you encountered are due to Expo Go limitations with native modules. Here are the solutions:

### Error Analysis:
- `requireNativeModule(ExpoCrypto): Using mock implementation` - Expo Go limitation
- `requireNativeModule(ExpoDocumentPicker): Using mock implementation` - Expo Go limitation  
- `Super expression must either be null or a function` - Class inheritance issue with Hermes

## 🔧 Testing Solutions

### Option 1: Expo Go Compatible Testing (Recommended for Quick Testing)

```bash
# Switch to Expo Go compatible mode
cd app
npm run expo-go

# This will:
# 1. Switch to simplified app version
# 2. Start Metro bundler
# 3. Show QR code for Expo Go
```

**Features in Expo Go Mode:**
- ✅ Simplified calculator interface
- ✅ Authentication flow demonstration
- ✅ Basic navigation
- ❌ No real BLE functionality
- ❌ No real crypto modules
- ❌ No native voice features

**Test Expression:** `12+5*7-3*2+8/4` (equals 45)

### Option 2: Development Build (Full Features)

```bash
# Install dependencies
cd app
npm install

# Create development build
npx expo prebuild
npx expo run:ios

# Or build for device
npx expo build:ios
```

**Features in Development Build:**
- ✅ Full calculator interface
- ✅ Real BLE authentication
- ✅ Native crypto modules
- ✅ Voice call functionality
- ✅ All security features

### Option 3: Minimal Debug Mode

```bash
# For debugging build issues
npm run minimal
```

## 📱 Testing Steps

### For Expo Go (Quick Test):

1. **Start the app:**
   ```bash
   cd app
   npm run expo-go
   ```

2. **Scan QR code** with Expo Go app on your iPhone

3. **Test calculator authentication:**
   - Enter: `12+5*7-3*2+8/4`
   - Press `=`
   - Should show authentication success dialog

4. **Navigate to chat** by tapping "Continue to Chat"

### For Development Build (Full Test):

1. **Prerequisites:**
   - Xcode installed
   - iOS Simulator or physical iPhone
   - Apple Developer account (for device testing)

2. **Build and run:**
   ```bash
   cd app
   npm install
   npx expo run:ios
   ```

3. **Test full features:**
   - Calculator authentication
   - BLE device scanning
   - Voice call initiation
   - Secure messaging

## 🔍 Troubleshooting

### Common Issues:

1. **"Super expression" error:**
   - Fixed in Expo Go compatible version
   - Use `npm run expo-go` for testing

2. **Native module errors:**
   - Expected in Expo Go
   - Use development build for full features

3. **Network connection issues:**
   - Update IP address in `app.config.js`
   - Ensure backend is running on `http://YOUR_IP:3000`

4. **Metro bundler issues:**
   ```bash
   npx expo start --clear
   ```

### Switch Between Modes:

```bash
# Switch to different app modes
node switch-app-mode.js expo-go    # Expo Go compatible
node switch-app-mode.js full       # Full app features  
node switch-app-mode.js minimal    # Debug mode
```

## 🎯 Testing Checklist

### Expo Go Mode:
- [ ] App loads without errors
- [ ] Calculator interface appears
- [ ] Authentication expression works
- [ ] Navigation to chat works
- [ ] Basic UI interactions work

### Development Build Mode:
- [ ] All Expo Go features work
- [ ] BLE scanning works
- [ ] Crypto modules work
- [ ] Voice recording works
- [ ] Device fingerprinting works
- [ ] Backend API calls work

## 📋 Next Steps

1. **For immediate testing:** Use Expo Go mode
2. **For full feature testing:** Create development build
3. **For production:** Build signed IPA with embedded secrets

## 🔗 Useful Commands

```bash
# Quick start with Expo Go
npm run expo-go

# Full app with development build
npm run full-app

# Debug mode
npm run minimal

# Switch modes manually
npm run switch-mode expo-go
npm run switch-mode full
npm run switch-mode minimal

# Clear cache and restart
npx expo start --clear

# Check what's running
npx expo whoami
```

## 📞 Backend Connection

Make sure your backend is running and accessible:

```bash
# In backend directory
npm run dev

# Test backend connectivity
curl http://YOUR_IP:3000/health
```

Update the IP address in `app/app.config.js` if needed:
```javascript
extra: {
  backendUrl: "http://YOUR_COMPUTER_IP:3000",
  frontendUrl: "http://YOUR_COMPUTER_IP:3001"
}
```
