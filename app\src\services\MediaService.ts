/**
 * Media Service
 * Handles document and media attachment functionality
 * - Max 50MB file size
 * - No screenshots allowed
 * - Documents and media only
 */

// Safe imports with fallback for Expo Go
let DocumentPicker: any = null;
let FileSystem: any = null;

try {
  DocumentPicker = require('expo-document-picker');
} catch (error) {
  console.log('📱 expo-document-picker not available, using fallback implementation');
  DocumentPicker = {
    getDocumentAsync: () => Promise.resolve({ canceled: true }),
  };
}

try {
  FileSystem = require('expo-file-system');
} catch (error) {
  console.log('📱 expo-file-system not available, using fallback implementation');
  FileSystem = {
    getInfoAsync: () => Promise.resolve({ exists: false, size: 0 }),
    deleteAsync: () => Promise.resolve(),
  };
}

import { Alert } from 'react-native';

export interface MediaAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  uri: string;
  mimeType?: string;
}

export interface MediaUploadResult {
  success: boolean;
  attachment?: MediaAttachment;
  error?: string;
}

export class MediaService {
  private static instance: MediaService;
  private readonly MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
  
  // Blocked file types (screenshots and system files)
  private readonly BLOCKED_TYPES = [
    'image/png', // Often screenshots
    'image/jpeg', // Often screenshots  
    'image/jpg', // Often screenshots
    'image/heic', // iPhone screenshots
    'image/webp', // Web screenshots
  ];

  // Allowed document types
  private readonly ALLOWED_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/csv',
    'application/zip',
    'application/x-zip-compressed',
    'audio/mpeg',
    'audio/wav',
    'audio/mp4',
    'video/mp4',
    'video/quicktime',
    'video/x-msvideo',
  ];

  private constructor() {}

  public static getInstance(): MediaService {
    if (!MediaService.instance) {
      MediaService.instance = new MediaService();
    }
    return MediaService.instance;
  }

  /**
   * Pick and validate a document/media file
   */
  public async pickDocument(): Promise<MediaUploadResult> {
    try {
      console.log('📎 Opening document picker...');

      const result = await DocumentPicker.getDocumentAsync({
        type: this.ALLOWED_TYPES,
        copyToCacheDirectory: true,
        multiple: false,
      });

      if (result.canceled) {
        return {
          success: false,
          error: 'File selection cancelled',
        };
      }

      const file = result.assets[0];
      
      // Validate file size
      if (file.size && file.size > this.MAX_FILE_SIZE) {
        Alert.alert(
          'File Too Large',
          `File size must be less than 50MB. Selected file is ${this.formatFileSize(file.size)}.`
        );
        return {
          success: false,
          error: 'File size exceeds 50MB limit',
        };
      }

      // Check for blocked types (screenshots)
      if (file.mimeType && this.BLOCKED_TYPES.includes(file.mimeType)) {
        Alert.alert(
          'File Type Not Allowed',
          'Screenshots and images are not permitted. Please select a document or media file.'
        );
        return {
          success: false,
          error: 'Screenshots and images are not allowed',
        };
      }

      // Additional validation for potential screenshots by filename
      if (this.isLikelyScreenshot(file.name)) {
        Alert.alert(
          'Screenshot Detected',
          'Screenshots are not permitted. Please select a document or media file.'
        );
        return {
          success: false,
          error: 'Screenshots are not allowed',
        };
      }

      const attachment: MediaAttachment = {
        id: `attachment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: file.name,
        type: this.getFileCategory(file.mimeType || ''),
        size: file.size || 0,
        uri: file.uri,
        mimeType: file.mimeType,
      };

      console.log('✅ File selected successfully:', {
        name: attachment.name,
        type: attachment.type,
        size: this.formatFileSize(attachment.size),
      });

      return {
        success: true,
        attachment,
      };

    } catch (error) {
      console.error('Document picker error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to select file',
      };
    }
  }

  /**
   * Check if filename suggests it's a screenshot
   */
  private isLikelyScreenshot(filename: string): boolean {
    const lowerName = filename.toLowerCase();
    const screenshotPatterns = [
      'screenshot',
      'screen shot',
      'img_',
      'image_',
      'photo_',
      'pic_',
      'capture',
      'snap_',
    ];

    return screenshotPatterns.some(pattern => lowerName.includes(pattern));
  }

  /**
   * Get file category for display
   */
  private getFileCategory(mimeType: string): string {
    if (mimeType.startsWith('application/pdf')) return 'PDF';
    if (mimeType.startsWith('application/msword') || 
        mimeType.includes('wordprocessingml')) return 'Word Document';
    if (mimeType.startsWith('application/vnd.ms-excel') || 
        mimeType.includes('spreadsheetml')) return 'Excel';
    if (mimeType.startsWith('application/vnd.ms-powerpoint') || 
        mimeType.includes('presentationml')) return 'PowerPoint';
    if (mimeType.startsWith('text/')) return 'Text File';
    if (mimeType.startsWith('audio/')) return 'Audio';
    if (mimeType.startsWith('video/')) return 'Video';
    if (mimeType.includes('zip')) return 'Archive';
    
    return 'Document';
  }

  /**
   * Format file size for display
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Validate attachment before sending
   */
  public validateAttachment(attachment: MediaAttachment): { valid: boolean; error?: string } {
    // Size check
    if (attachment.size > this.MAX_FILE_SIZE) {
      return {
        valid: false,
        error: 'File size exceeds 50MB limit',
      };
    }

    // Type check
    if (attachment.mimeType && this.BLOCKED_TYPES.includes(attachment.mimeType)) {
      return {
        valid: false,
        error: 'Screenshots and images are not allowed',
      };
    }

    // Screenshot name check
    if (this.isLikelyScreenshot(attachment.name)) {
      return {
        valid: false,
        error: 'Screenshots are not allowed',
      };
    }

    return { valid: true };
  }

  /**
   * Get file info for an existing attachment
   */
  public async getFileInfo(uri: string): Promise<{ size: number; exists: boolean }> {
    try {
      const info = await FileSystem.getInfoAsync(uri);
      return {
        size: info.exists && 'size' in info ? info.size : 0,
        exists: info.exists,
      };
    } catch (error) {
      console.error('Error getting file info:', error);
      return {
        size: 0,
        exists: false,
      };
    }
  }

  /**
   * Clean up temporary files
   */
  public async cleanupTempFile(uri: string): Promise<void> {
    try {
      if (uri.includes('DocumentPicker') || uri.includes('cache')) {
        await FileSystem.deleteAsync(uri, { idempotent: true });
        console.log('🗑️ Cleaned up temp file:', uri);
      }
    } catch (error) {
      console.error('Error cleaning up temp file:', error);
    }
  }
}

export default MediaService;
