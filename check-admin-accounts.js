const mongoose = require('mongoose');

// Admin schema (simplified for checking)
const AdminSchema = new mongoose.Schema({
  username: String,
  password: String,
  email: String,
  role: String,
  isActive: <PERSON><PERSON>an,
  authMethod: String,
  ppkEnabled: <PERSON><PERSON><PERSON>
}, { timestamps: true });

const Admin = mongoose.model('Admin', AdminSchema);

async function checkAdminAccounts() {
  try {
    // Connect to database
    await mongoose.connect('*************************************************************');
    console.log('Connected to database');

    // Check admin accounts
    const admins = await Admin.find({});
    console.log('Found admin accounts:', admins.length);
    
    if (admins.length > 0) {
      admins.forEach((admin, index) => {
        console.log(`\nAdmin ${index + 1}:`);
        console.log('  Username:', admin.username);
        console.log('  Email:', admin.email);
        console.log('  Role:', admin.role);
        console.log('  Active:', admin.isActive);
        console.log('  Auth Method:', admin.authMethod);
        console.log('  PPK Enabled:', admin.ppkEnabled);
        console.log('  Created:', admin.createdAt);
        console.log('  Updated:', admin.updatedAt);
      });
    } else {
      console.log('No admin accounts found');
    }

    await mongoose.disconnect();
  } catch (error) {
    console.error('Error checking admin accounts:', error);
  }
}

checkAdminAccounts();
