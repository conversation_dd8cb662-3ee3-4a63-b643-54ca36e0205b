/**
 * Device Fingerprinting Service
 * Creates unique device signatures for iOS authentication
 * Uses Expo-compatible APIs for SDK 53
 */

import * as Device from 'expo-device';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Crypto from 'expo-crypto';
import { Dimensions, Platform } from 'react-native';

export interface DeviceFingerprint {
  deviceId: string;
  deviceName: string;
  operatingSystem: string;
  systemVersion: string;
  screenProperties: {
    width: number;
    height: number;
    scale: number;
  };
  hardwareInfo: {
    modelName: string;
    brand: string;
    manufacturer: string;
  };
  timestamp: string;
  fingerprintHash: string;
}

export class DeviceService {
  private static instance: DeviceService;
  private cachedFingerprint: string | null = null;
  private fingerprintKey = 'device_fingerprint';

  private constructor() {}

  public static getInstance(): DeviceService {
    if (!DeviceService.instance) {
      DeviceService.instance = new DeviceService();
    }
    return DeviceService.instance;
  }

  /**
   * Generate comprehensive device fingerprint
   */
  public async getDeviceFingerprint(): Promise<string> {
    try {
      // Check if we have a cached fingerprint
      if (this.cachedFingerprint) {
        return this.cachedFingerprint;
      }

      // Try to load from storage first
      const storedFingerprint = await AsyncStorage.getItem(this.fingerprintKey);
      if (storedFingerprint) {
        this.cachedFingerprint = storedFingerprint;
        return storedFingerprint;
      }

      // Generate new fingerprint
      const fingerprint = await this.generateFingerprint();
      
      // Store for future use
      await AsyncStorage.setItem(this.fingerprintKey, fingerprint);
      this.cachedFingerprint = fingerprint;

      console.log('🔍 Generated new device fingerprint');
      return fingerprint;
    } catch (error) {
      console.error('Device fingerprint generation error:', error);
      // Return a fallback fingerprint
      return this.generateFallbackFingerprint();
    }
  }

  /**
   * Generate detailed device fingerprint
   */
  private async generateFingerprint(): Promise<string> {
    try {
      const screenProps = await this.getScreenProperties();
      const hardwareProps = await this.getHardwareProperties();
      
      const fingerprintData = {
        deviceId: Device.osInternalBuildId || 'unknown',
        deviceName: Device.deviceName || 'unknown',
        operatingSystem: Platform.OS,
        systemVersion: Device.osVersion || 'unknown',
        screenProperties: screenProps,
        hardwareInfo: hardwareProps,
        timestamp: new Date().toISOString(),
      };

      // Create hash from fingerprint data
      const dataString = JSON.stringify(fingerprintData);
      
      // Fallback hash implementation for when expo-crypto is not available
      let fingerprintHash: string;
      try {
        fingerprintHash = await Crypto.digestStringAsync(
          Crypto.CryptoDigestAlgorithm.SHA256,
          dataString,
          { encoding: Crypto.CryptoEncoding.HEX }
        );
      } catch (cryptoError) {
        console.log('📱 Using fallback fingerprint generation (expo-crypto not available)');
        // Simple hash alternative using built-in methods
        fingerprintHash = this.simpleHash(dataString);
      }

      const completeFingerprint: DeviceFingerprint = {
        ...fingerprintData,
        fingerprintHash,
      };

      return JSON.stringify(completeFingerprint);
    } catch (error) {
      console.error('Error generating device fingerprint:', error);
      throw error;
    }
  }

  /**
   * Get screen properties for fingerprinting
   */
  private async getScreenProperties(): Promise<any> {
    const { width, height, scale } = Dimensions.get('screen');
    
    return {
      width,
      height,
      scale,
      pixelRatio: scale,
    };
  }

  /**
   * Get hardware properties for fingerprinting
   */
  private async getHardwareProperties(): Promise<any> {
    return {
      modelName: Device.modelName || 'unknown',
      brand: Device.brand || 'unknown',
      manufacturer: Device.manufacturer || 'unknown',
      designName: Device.designName || 'unknown',
      deviceType: Device.deviceType || 'unknown',
      totalMemory: Device.totalMemory || 0,
    };
  }

  /**
   * Generate fallback fingerprint if main method fails
   */
  private generateFallbackFingerprint(): string {
    const fallbackData = {
      platform: Platform.OS,
      timestamp: Date.now(),
      random: Math.random().toString(36).substr(2, 9),
    };
    
    return JSON.stringify(fallbackData);
  }

  /**
   * Get basic device information for display
   */
  public async getBasicDeviceInfo(): Promise<{
    deviceName: string;
    modelName: string;
    osVersion: string;
    platform: string;
  }> {
    return {
      deviceName: Device.deviceName || 'Unknown Device',
      modelName: Device.modelName || 'Unknown Model',
      osVersion: Device.osVersion || 'Unknown Version',
      platform: Platform.OS,
    };
  }

  /**
   * Clear cached fingerprint (force regeneration)
   */
  public async clearCachedFingerprint(): Promise<void> {
    this.cachedFingerprint = null;
    await AsyncStorage.removeItem(this.fingerprintKey);
  }

  /**
   * Register device with backend (Expo Go development fallback)
   */
  public async registerDevice(authToken: string, backendUrl: string): Promise<boolean> {
    try {
      const fingerprint = await this.getDeviceFingerprint();
      const deviceInfo = await this.getBasicDeviceInfo();

      console.log('📱 Registering device with backend...', { deviceInfo });
      
      // In Expo Go, we'll just log the registration attempt
      console.log('✅ Device registration simulated (Expo Go environment)');
      return true;
    } catch (error) {
      console.error('Device registration error:', error);
      return false;
    }
  }

  /**
   * Verify device with backend (Expo Go development fallback)
   */
  public async verifyDevice(authToken: string, backendUrl: string): Promise<boolean> {
    try {
      const fingerprint = await this.getDeviceFingerprint();
      console.log('🔐 Verifying device with backend...', { fingerprint: fingerprint.substring(0, 50) + '...' });
      
      // In Expo Go, we'll just simulate verification
      console.log('✅ Device verification simulated (Expo Go environment)');
      return true;
    } catch (error) {
      console.error('Device verification error:', error);
      return false;
    }
  }

  /**
   * Simple hash function fallback for when expo-crypto is not available
   */
  private simpleHash(input: string): string {
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(16).padStart(8, '0');
  }
}

export default DeviceService;
