/**
 * Authentication Service
 * Handles calculator-to-chat authentication flow with backend integration
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { DeviceService } from './DeviceService';

export interface AuthConfig {
  backendUrl: string;
  frontendUrl: string;
}

export interface AuthenticationResult {
  success: boolean;
  token?: string;
  error?: string;
  shouldTransitionToChat?: boolean;
}

export class AuthService {
  private static instance: AuthService;
  private config: AuthConfig;
  private currentToken: string | null = null;
  private constructor() {
    // Default configuration - using Windows machine IP for iPhone testing
    this.config = {
      backendUrl: __DEV__ ? 'http://**************:3000' : 'https://your-backend-url.com',
      frontendUrl: __DEV__ ? 'http://**************:3001' : 'https://your-frontend-url.com',
    };
    this.loadStoredToken();
  }

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Main authentication flow for calculator
   * This is called when user taps "=" in calculator
   */
  public async authenticateExpression(expression: string): Promise<AuthenticationResult> {
    try {
      console.log('🔐 Starting authentication for expression:', expression);

      // Step 1: Normalize expression (remove spaces to match backend format)
      const normalizedExpression = expression.replace(/\s+/g, '');
      console.log('🔧 Normalized expression:', normalizedExpression);

      // Step 2: Get device fingerprint and generate BLE UUID
      const deviceFingerprint = await DeviceService.getInstance().getDeviceFingerprint();
      const bleUUID = `ble-${deviceFingerprint.substring(0, 8)}-${Date.now()}`;

      // Step 3: Authenticate with backend using normalized expression + device fingerprint + BLE UUID
      const authResult = await this.authenticateWithBackend(normalizedExpression, deviceFingerprint, bleUUID);
      
      if (authResult.success && authResult.token) {
        // Store token securely
        await this.storeToken(authResult.token);
        this.currentToken = authResult.token;
        
        console.log('🎉 Authentication successful - transitioning to chat');
        return {
          success: true,
          token: authResult.token,
          shouldTransitionToChat: true,
        };
      }

      console.log('❌ Backend authentication failed');
      return {
        success: false,
        error: authResult.error || 'Authentication failed',
        shouldTransitionToChat: false,
      };

    } catch (error) {
      console.error('Authentication error:', error);
      return {
        success: false,
        error: 'Authentication system error',
        shouldTransitionToChat: false,
      };
    }
  }
  /**
   * Authenticate with backend using expression-only authentication
   */
  private async authenticateWithBackend(
    expression: string,
    deviceFingerprint: string,
    bleUUID: string
  ): Promise<AuthenticationResult> {
    try {
      const response = await fetch(`${this.config.backendUrl}/api/auth/expression`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Device-Fingerprint': deviceFingerprint,
        },
        body: JSON.stringify({
          expression,
          deviceFingerprint,
          bleUUID,
        }),
      });

      const data = await response.json();
      
      console.log('🔍 Backend response:', data); // Debug log
      
      if (!response.ok) {
        throw new Error(data.error || 'Authentication failed');
      }

      // Backend returns token directly, not wrapped in success object
      const hasToken = data.token && data.token.length > 0;
      
      return {
        success: hasToken,
        token: data.token,
        error: hasToken ? undefined : 'No token received',
      };
    } catch (error) {
      console.error('Backend authentication error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  /**
   * Calculate normal mathematical result (for disguise)
   */
  public calculateExpression(expression: string): number | string {
    try {
      // Remove any non-mathematical characters for safety
      const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
      
      // Use Function constructor instead of eval for safety
      const result = Function(`"use strict"; return (${sanitized})`)();
      
      if (typeof result === 'number' && !isNaN(result)) {
        return result;
      }
      
      return 'Error';
    } catch (error) {
      console.error('Calculation error:', error);
      return 'Error';
    }
  }

  /**
   * Check if user is authenticated for chat
   */
  public async isAuthenticated(): Promise<boolean> {
    try {
      if (!this.currentToken) {
        await this.loadStoredToken();
      }

      if (!this.currentToken) {
        return false;
      }

      // Verify token with backend
      const response = await fetch(`${this.config.backendUrl}/api/auth/verify`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.currentToken}`,
        },
      });

      return response.ok;
    } catch (error) {
      console.error('Token verification error:', error);
      return false;
    }
  }

  /**
   * Get current authentication token
   */
  public getToken(): string | null {
    return this.currentToken;
  }

  /**
   * Logout and clear stored token
   */
  public async logout(): Promise<void> {
    try {
      if (this.currentToken) {
        // Notify backend of logout
        await fetch(`${this.config.backendUrl}/api/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.currentToken}`,
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage
      this.currentToken = null;
      await AsyncStorage.removeItem('auth_token');
    }
  }

  /**
   * Store token securely
   */
  private async storeToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem('auth_token', token);
    } catch (error) {
      console.error('Token storage error:', error);
    }
  }

  /**
   * Load stored token
   */
  private async loadStoredToken(): Promise<void> {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      this.currentToken = token;
    } catch (error) {
      console.error('Token loading error:', error);
      this.currentToken = null;
    }
  }

  /**
   * Update configuration
   */
  public updateConfig(config: Partial<AuthConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Get current configuration
   */
  public getConfig(): AuthConfig {
    return { ...this.config };
  }

  /**
   * Get device fingerprint (delegates to DeviceService)
   */
  public async getDeviceFingerprint(): Promise<string> {
    return await DeviceService.getInstance().getDeviceFingerprint();
  }
}

export default AuthService;
