/**
 * Chat Service
 * Handles messaging functionality for mobile app
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthService } from './AuthService';
import { MediaAttachment } from './MediaService';

export interface ChatMessage {
  id: string;
  chatId: string;
  text: string;
  sender: 'user' | 'superuser';
  timestamp: number;
  status: 'sent' | 'delivered' | 'read' | 'failed';
  isDelivered?: boolean;
  isRead?: boolean;
  attachment?: MediaAttachment;
}

export interface ChatStatus {
  isConnected: boolean;
  superuserOnline: boolean;
  lastActivity?: number;
}

export interface SendMessageResult {
  success: boolean;
  message?: ChatMessage;
  error?: string;
}

export interface GetMessagesResult {
  success: boolean;
  messages?: ChatMessage[];
  error?: string;
}

export class ChatService {
  private static instance: ChatService;
  private authService: AuthService;
  private backendUrl: string;

  private constructor() {
    this.authService = AuthService.getInstance();
    this.backendUrl = this.authService.getConfig().backendUrl;
  }

  public static getInstance(): ChatService {
    if (!ChatService.instance) {
      ChatService.instance = new ChatService();
    }
    return ChatService.instance;
  }

  /**
   * Send message to superuser
   */
  public async sendMessage(message: string, attachment?: MediaAttachment): Promise<SendMessageResult> {
    try {
      const logMessage = attachment 
        ? `📤 Sending message with attachment: ${attachment.name}` 
        : `📤 Sending message: ${message.substring(0, 50)}...`;
      console.log(logMessage);

      const token = this.authService.getToken();
      if (!token) {
        return {
          success: false,
          error: 'Authentication required',
        };
      }

      let requestBody;
      let headers: Record<string, string> = {
        'Authorization': `Bearer ${token}`,
      };

      if (attachment) {
        // Send as multipart/form-data for file upload
        const formData = new FormData();
        formData.append('message', message.trim());
        formData.append('timestamp', Date.now().toString());
        
        // Create file object for upload
        const fileUri = attachment.uri;
        const fileName = attachment.name;
        const fileType = attachment.mimeType || 'application/octet-stream';
        
        formData.append('attachment', {
          uri: fileUri,
          name: fileName,
          type: fileType,
        } as any);

        requestBody = formData;
        // Don't set Content-Type header, let fetch set it for multipart/form-data
      } else {
        // Send as JSON for text-only message
        headers['Content-Type'] = 'application/json';
        requestBody = JSON.stringify({
          message: message.trim(),
          timestamp: Date.now(),
        });
      }

      const response = await fetch(`${this.backendUrl}/api/chat/mobile/send`, {
        method: 'POST',
        headers,
        body: requestBody,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send message');
      }

      if (data.success) {
        const chatMessage: ChatMessage = {
          id: data.message.id,
          chatId: data.message.chatId,
          text: message.trim(),
          sender: 'user',
          timestamp: data.message.timestamp,
          status: 'sent',
          isDelivered: false,
          isRead: false,
          attachment: attachment,
        };

        console.log('✅ Message sent successfully:', chatMessage.id);
        return {
          success: true,
          message: chatMessage,
        };
      }

      return {
        success: false,
        error: data.error || 'Unknown error',
      };

    } catch (error) {
      console.error('Send message error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  /**
   * Get chat messages from backend
   */  public async getMessages(limit: number = 50, offset: number = 0): Promise<GetMessagesResult> {
    try {
      console.log('📥 Getting messages...');

      const token = this.authService.getToken();
      if (!token) {
        return {
          success: false,
          error: 'Authentication required',
        };
      }

      const response = await fetch(
        `${this.backendUrl}/api/chat/mobile/messages?limit=${limit}&offset=${offset}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
        }
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to get messages');
      }

      if (data.success) {
        const messages: ChatMessage[] = (data.messages || []).map((msg: any) => ({
          id: msg.messageId || msg.id,
          chatId: msg.chatId,
          text: msg.content?.text || msg.text || '',
          sender: msg.senderId === data.currentUserId ? 'user' : 'superuser',
          timestamp: new Date(msg.metadata?.sentAt || msg.sentAt || msg.timestamp).getTime(),
          status: msg.status || 'delivered',
          isDelivered: true,
          isRead: msg.readAt ? true : false,
        }));

        console.log('✅ Retrieved messages:', messages.length);
        return {
          success: true,
          messages,
        };
      }

      return {
        success: false,
        error: data.error || 'Unknown error',
      };

    } catch (error) {
      console.error('Get messages error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  /**
   * Get chat status (connection, superuser online status)
   */  public async getChatStatus(): Promise<ChatStatus> {
    try {
      const token = this.authService.getToken();
      if (!token) {
        return {
          isConnected: false,
          superuserOnline: false,
        };
      }

      const response = await fetch(`${this.backendUrl}/api/chat/mobile/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (response.ok && data.success) {
        return {
          isConnected: true,
          superuserOnline: data.status?.superuserOnline || false,
          lastActivity: data.status?.lastActivity ? new Date(data.status.lastActivity).getTime() : undefined,
        };
      }

      return {
        isConnected: false,
        superuserOnline: false,
      };

    } catch (error) {
      console.error('Get chat status error:', error);
      return {
        isConnected: false,
        superuserOnline: false,
      };
    }
  }

  /**
   * Clear local chat data (on logout)
   */
  public async clearChatData(): Promise<void> {
    try {
      await AsyncStorage.removeItem('chat_messages');
      await AsyncStorage.removeItem('chat_drafts');
      console.log('Chat data cleared');
    } catch (error) {
      console.error('Failed to clear chat data:', error);
    }
  }

  /**
   * Cache messages locally for offline access
   */
  private async cacheMessages(messages: ChatMessage[]): Promise<void> {
    try {
      await AsyncStorage.setItem('chat_messages', JSON.stringify(messages));
    } catch (error) {
      console.error('Failed to cache messages:', error);
    }
  }

  /**
   * Get cached messages for offline access
   */
  public async getCachedMessages(): Promise<ChatMessage[]> {
    try {
      const cached = await AsyncStorage.getItem('chat_messages');
      return cached ? JSON.parse(cached) : [];
    } catch (error) {
      console.error('Failed to get cached messages:', error);
      return [];
    }
  }

  /**
   * Get available chats/users
   */
  public async getAvailableChats(): Promise<{ success: boolean; chats?: any[]; error?: string }> {
    try {
      const token = this.authService.getToken();
      if (!token) {
        return {
          success: false,
          error: 'Authentication required',
        };
      }

      const response = await fetch(`${this.backendUrl}/api/chat/mobile/users`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to get available chats');
      }

      return {
        success: true,
        chats: data.users || [],
      };

    } catch (error) {
      console.error('Get available chats error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  /**
   * Get chat users list
   */
  public async getChatUsers(): Promise<{ success: boolean; users?: any[]; currentUser?: any; error?: string }> {
    try {
      console.log('📥 Getting chat users...');

      const token = this.authService.getToken();
      if (!token) {
        return {
          success: false,
          error: 'Authentication required',
        };
      }

      const response = await fetch(
        `${this.backendUrl}/api/chat/mobile/users`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
            'x-device-fingerprint': await this.authService.getDeviceFingerprint(),
          },
        }
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to get chat users');
      }

      if (data.success) {
        console.log('✅ Retrieved chat users:', data.users?.length || 0);
        return {
          success: true,
          users: data.users || [],
          currentUser: data.currentUser,
        };
      }

      return {
        success: false,
        error: data.error || 'Unknown error',
      };

    } catch (error) {
      console.error('Get chat users error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }
}

export default ChatService;
