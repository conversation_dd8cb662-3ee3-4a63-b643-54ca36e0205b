/**
 * Chat Screen Component
 * Superuser-only messaging with modern iOS design
 */

import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
// Safe haptics import with fallback for Expo Go
let Haptics: any = null;
try {
  Haptics = require('expo-haptics');
} catch (error) {
  console.log('📱 expo-haptics not available, using fallback implementation');
  Haptics = {
    impactAsync: () => Promise.resolve(),
    ImpactFeedbackStyle: {
      Light: 'light',
      Medium: 'medium',
      Heavy: 'heavy'
    }
  };
}
import { theme } from '../utils/theme';
import { AuthService } from '../services/AuthService';
import { ChatService, ChatMessage } from '../services/ChatService';
import { ChatListItem } from './ChatListScreen';
import { MediaService, MediaAttachment } from '../services/MediaService';

interface ChatScreenProps {
  chatUser?: ChatListItem;
  onBack?: () => void;
  onLogout: () => void;
}

export const ChatScreen: React.FC<ChatScreenProps> = ({ chatUser, onBack, onLogout }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [superuserOnline, setSuperuserOnline] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedAttachment, setSelectedAttachment] = useState<MediaAttachment | null>(null);
  
  const flatListRef = useRef<FlatList>(null);
  const authService = AuthService.getInstance();
  const chatService = ChatService.getInstance();
  const mediaService = MediaService.getInstance();

  // Initialize chat connection
  useEffect(() => {
    initializeChat();
    
    // Poll for new messages periodically
    const messagePollingInterval = setInterval(() => {
      loadMessages();
    }, 5000); // Check every 5 seconds

    // Poll for status periodically
    const statusPollingInterval = setInterval(() => {
      updateChatStatus();
    }, 10000); // Check every 10 seconds

    return () => {
      clearInterval(messagePollingInterval);
      clearInterval(statusPollingInterval);
    };
  }, []);

  const initializeChat = async () => {
    try {
      setIsLoading(true);
      await loadMessages();
      await updateChatStatus();
    } catch (error) {
      console.error('Failed to initialize chat:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadMessages = async () => {
    try {
      const result = await chatService.getMessages(50, 0);
      if (result.success && result.messages) {
        setMessages(result.messages.sort((a, b) => a.timestamp - b.timestamp));
        
        // Scroll to bottom after loading messages
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      } else {
        console.error('Failed to load messages:', result.error);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };
  const updateChatStatus = async () => {
    try {
      const status = await chatService.getChatStatus();
      setIsConnected(status.isConnected);
      setSuperuserOnline(status.superuserOnline);
    } catch (error) {
      console.error('Error updating chat status:', error);
    }
  };
  // Demo setup effect
  useEffect(() => {
    const demoMessages: ChatMessage[] = [
      {
        id: '1',
        chatId: 'default-chat',
        text: 'Welcome to CCALC secure messaging. Your device has been authenticated.',
        sender: 'superuser',
        timestamp: Date.now() - 60000,
        status: 'delivered',
        isDelivered: true,
        isRead: true,
      },
      {
        id: '2',
        chatId: 'default-chat',
        text: 'All communications are end-to-end encrypted.',
        sender: 'superuser',
        timestamp: Date.now() - 30000,
        status: 'delivered',
        isDelivered: true,
        isRead: true,
      },
    ];
      setMessages(demoMessages);
    setIsConnected(true);
    setSuperuserOnline(true);
  }, []);

  const sendMessage = useCallback(async () => {
    if ((!inputText.trim() && !selectedAttachment) || !isConnected) return;

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    const messageId = Date.now().toString();
    const newMessage: ChatMessage = {
      id: messageId,
      chatId: 'default-chat',
      text: inputText.trim() || (selectedAttachment ? `📎 ${selectedAttachment.name}` : ''),
      sender: 'user',
      timestamp: Date.now(),
      status: 'sent',
      isDelivered: false,
      isRead: false,
      attachment: selectedAttachment || undefined,
    };

    // Add message to local state immediately
    setMessages(prev => [...prev, newMessage]);
    setInputText('');
    const attachmentToSend = selectedAttachment;
    setSelectedAttachment(null);

    // Scroll to bottom
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);

    try {
      // Use the updated ChatService sendMessage method with attachment
      const result = await chatService.sendMessage(newMessage.text, attachmentToSend || undefined);

      if (result.success) {
        // Update message status
        setMessages(prev => prev.map(msg => 
          msg.id === messageId 
            ? { ...msg, status: 'delivered', isDelivered: true }
            : msg
        ));
      } else {
        // Mark message as failed
        setMessages(prev => prev.map(msg => 
          msg.id === messageId 
            ? { ...msg, status: 'failed' }
            : msg
        ));
        
        Alert.alert('Send Failed', result.error || 'Failed to send message');
      }
    } catch (error) {
      console.error('Send message error:', error);
      
      // Mark message as failed
      setMessages(prev => prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, status: 'failed' }
          : msg
      ));
      
      Alert.alert('Error', 'Failed to send message');
    }
  }, [inputText, selectedAttachment, isConnected, chatService]);

  // Simulate superuser response (for demo purposes)
  const simulateSuperuserResponse = (userMessage: string) => {
    const responses = [
      'Message received and acknowledged.',
      'Thank you for your message. Processing your request.',
      'Understood. Please wait for further instructions.',
      'Your message has been logged securely.',
      'Response will be provided shortly.',
    ];

    const responseText = responses[Math.floor(Math.random() * responses.length)];
      const responseMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      chatId: 'default-chat', // Using default chat ID
      text: responseText,
      sender: 'superuser',
      timestamp: Date.now(),
      status: 'delivered',
      isDelivered: true,
      isRead: false,
    };

    setMessages(prev => [...prev, responseMessage]);
    
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };
  const handleLogout = useCallback(async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    Alert.alert(
      'Logout',
      'Are you sure you want to logout? This will clear your session.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await authService.logout();
            onLogout();
          },
        },
      ]
    );
  }, [authService, onLogout]);  const initiateVoiceCall = useCallback(async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    try {
      Alert.alert(
        'Secure Voice Call',
        'Start encrypted voice call with voice morphing enabled?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Start Call',
            onPress: async () => {
              try {
                console.log('🎤 Starting voice call with morphing...');
                // Import VoiceService
                const { VoiceService } = await import('../services/VoiceService');
                const voiceService = VoiceService.getInstance();
                
                // Start call with automatic recording and morphing
                // Note: For regular users, they use 'agent' role when calling superuser
                const call = await voiceService.startVoiceCall('superuser', 'agent');
                
                Alert.alert(
                  'Call Connected', 
                  `Secure voice call active with ${call.morphingProfile} profile. Recording enabled for security.`,
                  [
                    {
                      text: 'End Call',
                      style: 'destructive',
                      onPress: () => voiceService.endVoiceCall()
                    }
                  ]
                );
                
              } catch (error) {
                console.error('❌ Voice call failed:', error);
                Alert.alert('Call Failed', 'Unable to establish secure voice connection.');
              }
            },
          },
        ]
      );
    } catch (error) {
      console.error('❌ Voice call initialization failed:', error);
      Alert.alert('Error', 'Unable to initialize voice call system.');
    }
  }, []);

  // Handle file attachment
  const handleAttachment = useCallback(async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      
      const result = await mediaService.pickDocument();
      
      if (result.success && result.attachment) {
        setSelectedAttachment(result.attachment);
        console.log('📎 Attachment selected:', result.attachment.name);
      } else if (result.error) {
        Alert.alert('Attachment Error', result.error);
      }
    } catch (error) {
      console.error('Attachment error:', error);
      Alert.alert('Error', 'Failed to select attachment');
    }
  }, [mediaService]);

  // Remove selected attachment
  const removeAttachment = useCallback(() => {
    setSelectedAttachment(null);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const renderMessage = ({ item }: { item: ChatMessage }) => {
    const isUser = item.sender === 'user';
    
    return (
      <View style={[
        styles.messageContainer,
        isUser ? styles.userMessageContainer : styles.superuserMessageContainer
      ]}>
        <View style={[
          styles.messageBubble,
          isUser ? styles.userMessage : styles.superuserMessage
        ]}>
          {/* Show attachment if present */}
          {item.attachment && (
            <View style={styles.messageAttachment}>
              <Text style={styles.attachmentIcon}>📎</Text>
              <View style={styles.messageAttachmentInfo}>
                <Text style={[
                  styles.messageAttachmentName,
                  isUser ? styles.userMessageText : styles.superuserMessageText
                ]} numberOfLines={1}>
                  {item.attachment.name}
                </Text>
                <Text style={[
                  styles.messageAttachmentMeta,
                  isUser ? styles.userTimestamp : styles.superuserTimestamp
                ]}>
                  {item.attachment.type} • {Math.round(item.attachment.size / 1024)}KB
                </Text>
              </View>
            </View>
          )}
          
          {/* Show text if present */}
          {item.text && !item.text.startsWith('📎') && (
            <Text style={[
              styles.messageText,
              isUser ? styles.userMessageText : styles.superuserMessageText
            ]}>
              {item.text}
            </Text>
          )}
          
          <View style={styles.messageFooter}>
            <Text style={[
              styles.timestamp,
              isUser ? styles.userTimestamp : styles.superuserTimestamp
            ]}>
              {new Date(item.timestamp).toLocaleTimeString([], { 
                hour: '2-digit', 
                minute: '2-digit' 
              })}
            </Text>
            {isUser && (
              <Text style={styles.deliveryStatus}>
                {item.isDelivered ? '✓' : '⏳'}
              </Text>
            )}
          </View>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          {onBack ? (
            <TouchableOpacity onPress={onBack} style={styles.backButton}>
              <Text style={styles.backText}>← Back</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity onPress={handleLogout} style={styles.backButton}>
              <Text style={styles.backText}>← Exit</Text>
            </TouchableOpacity>
          )}
        </View>
        
        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>
            {chatUser?.displayName || 'Secure Chat'}
          </Text>
          <Text style={styles.headerSubtitle}>
            {isConnected 
              ? (chatUser?.isOnline ? '🟢 Online' : '🟡 Connecting...') 
              : '🔴 Offline'
            }
          </Text>
        </View>
        
        <View style={styles.headerRight}>
          <TouchableOpacity onPress={initiateVoiceCall} style={styles.callButton}>
            <Text style={styles.callButtonText}>📞</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Messages List */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        showsVerticalScrollIndicator={false}
      />

      {/* Typing Indicator */}
      {isTyping && (
        <View style={styles.typingContainer}>
          <Text style={styles.typingText}>Superuser is typing...</Text>
        </View>
      )}

      {/* Input Area */}
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        {/* Attachment Preview */}
        {selectedAttachment && (
          <View style={styles.attachmentPreview}>
            <View style={styles.attachmentInfo}>
              <Text style={styles.attachmentButtonText}>📎</Text>
              <View style={styles.attachmentDetails}>
                <Text style={styles.attachmentName} numberOfLines={1}>
                  {selectedAttachment.name}
                </Text>
                <Text style={styles.attachmentSize}>
                  {selectedAttachment.type} • {Math.round(selectedAttachment.size / 1024)}KB
                </Text>
              </View>
            </View>
            <TouchableOpacity
              style={styles.removeAttachmentButton}
              onPress={removeAttachment}
            >
              <Text style={styles.removeAttachmentText}>×</Text>
            </TouchableOpacity>
          </View>
        )}

        <View style={styles.inputContainer}>
          <TouchableOpacity
            style={styles.attachmentButton}
            onPress={handleAttachment}
            disabled={!isConnected}
          >
            <Text style={styles.attachmentButtonText}>📎</Text>
          </TouchableOpacity>
          
          <TextInput
            style={styles.textInput}
            value={inputText}
            onChangeText={setInputText}
            placeholder="Type a secure message..."
            placeholderTextColor={theme.colors.placeholderText}
            multiline
            maxLength={1000}
            editable={isConnected}
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              (!inputText.trim() && !selectedAttachment || !isConnected) && styles.sendButtonDisabled
            ]}
            onPress={sendMessage}
            disabled={(!inputText.trim() && !selectedAttachment) || !isConnected}
          >
            <Text style={styles.sendButtonText}>→</Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.systemGray5,
    backgroundColor: theme.colors.tertiaryBackground,
  },
  headerLeft: {
    flex: 1,
  },
  headerCenter: {
    flex: 2,
    alignItems: 'center',
  },
  headerRight: {
    flex: 1,
    alignItems: 'flex-end',
  },
  headerTitle: {
    fontSize: theme.typography.sizes.headline,
    fontWeight: theme.typography.weights.semibold,
    color: theme.colors.label,
    fontFamily: theme.typography.families.system,
  },
  headerSubtitle: {
    fontSize: theme.typography.sizes.caption,
    color: theme.colors.secondaryLabel,
    fontFamily: theme.typography.families.system,
    marginTop: 2,
  },
  backButton: {
    paddingVertical: theme.spacing.xs,
  },
  backText: {
    fontSize: theme.typography.sizes.body,
    color: theme.colors.systemBlue,
    fontFamily: theme.typography.families.system,
  },
  callButton: {
    padding: theme.spacing.xs,
  },
  callButtonText: {
    fontSize: theme.typography.sizes.title2,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: theme.spacing.md,
  },
  messageContainer: {
    paddingHorizontal: theme.spacing.md,
    marginVertical: theme.spacing.xs / 2,
  },
  userMessageContainer: {
    alignItems: 'flex-end',
  },
  superuserMessageContainer: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.small,
  },
  userMessage: {
    backgroundColor: theme.colors.messageBackground,
    borderBottomRightRadius: theme.borderRadius.xs,
  },
  superuserMessage: {
    backgroundColor: theme.colors.incomingMessage,
    borderBottomLeftRadius: theme.borderRadius.xs,
  },
  messageText: {
    fontSize: theme.typography.sizes.body,
    lineHeight: 22,
    fontFamily: theme.typography.families.systemText,
  },
  userMessageText: {
    color: theme.colors.background,
  },
  superuserMessageText: {
    color: theme.colors.label,
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: theme.spacing.xs,
  },
  timestamp: {
    fontSize: theme.typography.sizes.caption2,
    fontFamily: theme.typography.families.system,
  },
  userTimestamp: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  superuserTimestamp: {
    color: theme.colors.tertiaryLabel,
  },
  deliveryStatus: {
    fontSize: theme.typography.sizes.caption2,
    color: 'rgba(255, 255, 255, 0.7)',
    marginLeft: theme.spacing.xs,
  },
  typingContainer: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
  },
  typingText: {
    fontSize: theme.typography.sizes.caption,
    color: theme.colors.secondaryLabel,
    fontStyle: 'italic',
    fontFamily: theme.typography.families.system,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.tertiaryBackground,
    borderTopWidth: 1,
    borderTopColor: theme.colors.systemGray5,
  },
  textInput: {
    flex: 1,
    minHeight: 36,
    maxHeight: 100,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.secondaryBackground,
    borderRadius: theme.borderRadius.lg,
    fontSize: theme.typography.sizes.body,
    color: theme.colors.label,
    fontFamily: theme.typography.families.systemText,
    textAlignVertical: 'top',
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: theme.colors.systemBlue,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: theme.spacing.sm,
  },
  sendButtonDisabled: {
    backgroundColor: theme.colors.systemGray3,
  },
  sendButtonText: {
    fontSize: theme.typography.sizes.title2,
    color: theme.colors.background,
    fontWeight: theme.typography.weights.semibold,
  },
  // Attachment styles
  attachmentPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.sm,
    marginHorizontal: theme.spacing.md,
    marginTop: theme.spacing.sm,
    backgroundColor: theme.colors.systemGray6,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.systemGray5,
  },
  attachmentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  attachmentDetails: {
    flex: 1,
  },
  attachmentName: {
    fontSize: theme.typography.sizes.footnote,
    fontWeight: theme.typography.weights.medium,
    color: theme.colors.label,
  },
  attachmentSize: {
    fontSize: theme.typography.sizes.caption2,
    color: theme.colors.secondaryLabel,
    marginTop: 2,
  },
  removeAttachmentButton: {
    padding: theme.spacing.xs,
    borderRadius: theme.borderRadius.circle,
    backgroundColor: theme.colors.systemRed,
  },
  removeAttachmentText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: theme.typography.weights.bold,
  },
  attachmentButton: {
    padding: theme.spacing.sm,
    marginRight: theme.spacing.xs,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.systemGray6,
  },
  attachmentButtonText: {
    fontSize: 18,
  },
  // Message attachment styles
  messageAttachment: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
    padding: theme.spacing.xs,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: theme.borderRadius.sm,
  },
  attachmentIcon: {
    fontSize: 16,
    marginRight: theme.spacing.xs,
  },
  messageAttachmentInfo: {
    flex: 1,
  },
  messageAttachmentName: {
    fontSize: theme.typography.sizes.footnote,
    fontWeight: theme.typography.weights.medium,
    marginBottom: 2,
  },
  messageAttachmentMeta: {
    fontSize: theme.typography.sizes.caption2,
    opacity: 0.8,
  },
});

export default ChatScreen;
