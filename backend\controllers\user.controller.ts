import { Request, Response, Router } from 'express';
import User from '../models/User';
import bcrypt from 'bcrypt';
import argon2 from 'argon2';
import { auth, requireAdmin } from '../middleware/auth';
import validate from '../middleware/validate';
import Joi from 'joi';

// Validation schemas
const createUserSchema = Joi.object({
  username: Joi.string().required(),
  expression: Joi.string().required(),
  displayName: Joi.string().required(),
  deviceFingerprint: Joi.string().required(),
  bleUUID: Joi.string().required(),
  isSuperuser: Joi.boolean().optional()
});

const updateUserSchema = Joi.object({
  displayName: Joi.string().optional(),
  status: Joi.string().valid('active', 'inactive', 'locked').optional(),
  isSuperuser: Joi.boolean().optional()
});

const resetDeviceSchema = Joi.object({
  deviceFingerprint: Joi.string().required(),
  bleUUID: Joi.string().required()
});

// GET /api/users - List all users
export async function listUsersController(req: Request, res: Response): Promise<void> {
  try {
    const users = await User.find({}, '-expressionHash -deviceFingerprintHash -bleUUIDHash'); // Never expose hashes
    res.status(200).json(users);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch users' });
  }
}

// POST /api/users - Create new user
export async function createUserController(req: Request, res: Response): Promise<void> {
  try {
    const { username, expression, displayName, deviceFingerprint, bleUUID, isSuperuser } = req.body;
    if (!username || !expression || !displayName || !deviceFingerprint || !bleUUID) {
      res.status(400).json({ error: 'Missing required fields' });
      return;
    }
    // Hash expression, deviceFingerprint, and bleUUID
    const expressionHash = await bcrypt.hash(expression, 12);
    const deviceFingerprintHash = await bcrypt.hash(deviceFingerprint, 12);
    const bleUUIDHash = await bcrypt.hash(bleUUID, 12);
    // If creating a superuser, ensure all other users are not superuser
    if (isSuperuser === true) {
      await User.updateMany({}, { $set: { isSuperuser: false } });
    }
    const user = new User({
      username,
      expressionHash,
      profile: { displayName },
      deviceFingerprintHash,
      bleUUIDHash,
      isSuperuser: !!isSuperuser,
      status: 'active',
    });
    await user.save();
    res.status(201).json({ id: user._id, username: user.username });
  } catch (err: any) {
    if (err.code === 11000) {
      res.status(409).json({ error: 'Username already exists' });
    } else {
      res.status(500).json({ error: 'Failed to create user' });
    }
  }
}

// GET /api/users/:id - Fetch specific user details
export async function getUserController(req: Request, res: Response): Promise<void> {
  try {
    const user = await User.findById(req.params.id, '-expressionHash -deviceFingerprintHash -bleUUIDHash');
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }
    res.status(200).json(user);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch user' });
  }
}

// PUT /api/users/:id - Update user details
export async function updateUserController(req: Request, res: Response): Promise<void> {
  try {
    const { displayName, status, isSuperuser } = req.body;
    const user = await User.findById(req.params.id);
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }
    if (displayName) user.profile.displayName = displayName;
    if (status) user.status = status;
    // If setting isSuperuser true, ensure all other users are not superuser
    if (typeof isSuperuser === 'boolean' && isSuperuser === true) {
      await User.updateMany({ _id: { $ne: user._id } }, { $set: { isSuperuser: false } });
      user.isSuperuser = true;
    } else if (typeof isSuperuser === 'boolean') {
      user.isSuperuser = isSuperuser;
    }
    await user.save();
    res.status(200).json({ message: 'User updated' });
  } catch (err) {
    res.status(500).json({ error: 'Failed to update user' });
  }
}

// DELETE /api/users/:id - Deactivate or delete user
export async function deleteUserController(req: Request, res: Response): Promise<void> {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }
    user.status = 'inactive';
    await user.save();
    res.status(200).json({ message: 'User deactivated' });
  } catch (err) {
    res.status(500).json({ error: 'Failed to deactivate user' });
  }
}

// POST /api/users/:id/reset - Reset user device fingerprint/BLE configuration
export async function resetUserDeviceController(req: Request, res: Response): Promise<void> {
  try {
    const { deviceFingerprint, bleUUID } = req.body;
    if (!deviceFingerprint || !bleUUID) {
      res.status(400).json({ error: 'Missing required fields' });
      return;
    }
    const user = await User.findById(req.params.id);
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }
    user.deviceFingerprintHash = await bcrypt.hash(deviceFingerprint, 12);
    user.bleUUIDHash = await bcrypt.hash(bleUUID, 12);
    await user.save();
    res.status(200).json({ message: 'Device/BLE reset' });
  } catch (err) {
    res.status(500).json({ error: 'Failed to reset device/BLE' });
  }
}

// Create and configure router
const router = Router();

// Define routes with middleware and validation
router.get('/', auth, requireAdmin, listUsersController);
router.post('/', auth, requireAdmin, validate(createUserSchema), createUserController);
router.get('/:id', auth, requireAdmin, getUserController);
router.put('/:id', auth, requireAdmin, validate(updateUserSchema), updateUserController);
router.delete('/:id', auth, requireAdmin, deleteUserController);
router.post('/:id/reset', auth, requireAdmin, validate(resetDeviceSchema), resetUserDeviceController);

export default router;
