import crypto from 'crypto';

const ALGORITHM = 'aes-256-gcm';

export function encryptMessage(text: string, key?: string): { encrypted: string; iv: string; tag: string; keyHash: string } {
  const encryptionKey = key || crypto.randomBytes(32);
  const keyBuffer = typeof encryptionKey === 'string' ? Buffer.from(encryptionKey, 'hex') : encryptionKey;
  const iv = crypto.randomBytes(16);
  
  const cipher = crypto.createCipheriv(ALGORITHM, keyBuffer, iv);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  const tag = cipher.getAuthTag();
  
  return {
    encrypted,
    iv: iv.toString('hex'),
    tag: tag.toString('hex'),
    keyHash: crypto.createHash('sha256').update(keyBuffer).digest('hex')
  };
}

export function decryptMessage(encryptedData: { encrypted: string; iv: string; tag: string }, key: string): string {
  const keyBuffer = Buffer.from(key, 'hex');
  const decipher = crypto.createDecipheriv(ALGORITHM, keyBuffer, Buffer.from(encryptedData.iv, 'hex'));
  decipher.setAuthTag(Buffer.from(encryptedData.tag, 'hex'));
  
  let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
}

export function encryptFile(buffer: Buffer, key?: string, _iv?: string): { encrypted: Buffer; iv: string; tag: string; keyHash: string } {
  const encryptionKey = key || crypto.randomBytes(32);
  const keyBuffer = typeof encryptionKey === 'string' ? Buffer.from(encryptionKey, 'hex') : encryptionKey;
  const iv = crypto.randomBytes(16);
  
  const cipher = crypto.createCipheriv(ALGORITHM, keyBuffer, iv);
  const encrypted = Buffer.concat([cipher.update(buffer), cipher.final()]);
  const tag = cipher.getAuthTag();
  
  return {
    encrypted,
    iv: iv.toString('hex'),
    tag: tag.toString('hex'),
    keyHash: crypto.createHash('sha256').update(keyBuffer).digest('hex')
  };
}

export function decryptFile(encryptedData: { encrypted: Buffer; iv: string; tag: string }, key: string, _iv?: string): Buffer {
  const keyBuffer = Buffer.from(key, 'hex');
  const decipher = crypto.createDecipheriv(ALGORITHM, keyBuffer, Buffer.from(encryptedData.iv, 'hex'));
  decipher.setAuthTag(Buffer.from(encryptedData.tag, 'hex'));
  
  return Buffer.concat([decipher.update(encryptedData.encrypted), decipher.final()]);
}

export function generateEncryptionKey(): string {
  return crypto.randomBytes(32).toString('hex');
}
