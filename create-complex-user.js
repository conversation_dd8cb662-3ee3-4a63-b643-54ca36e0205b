const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

// MongoDB connection string
const mongoUri = '*************************************************************';

// User schema
const UserSchema = new mongoose.Schema({
  username: { type: String, required: true, unique: true, trim: true },
  email: { type: String, trim: true },
  expressionHash: { type: String, required: true },
  unlockExpression: { type: String },
  expressionType: { 
    type: String, 
    enum: ['calculator', 'pattern'], 
    default: 'calculator' 
  },
  expressionUpdatedAt: { type: Date },
  profile: {
    displayName: { type: String, required: true },
  },
  deviceFingerprintHash: { type: String },
  deviceMetadata: {
    model: { type: String },
    os: { type: String },
    registeredAt: { type: Date },
    registrationCoords: {
      lat: { type: Number },
      lng: { type: Number },
    },
  },
  bleUUIDHash: { type: String },
  status: {
    type: String,
    enum: ['active', 'inactive', 'locked', 'pending_device_registration'],
    default: 'pending_device_registration',
  },
  lastLoginAt: { type: Date },
  failedLoginAttempts: { type: Number, default: 0 },
  lockUntil: { type: Date },
  builds: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Build' }],
  messageExpiry: { type: Date },
  isSuperuser: { type: Boolean, default: false },
}, { timestamps: true });

const User = mongoose.model('User', UserSchema);

async function createComplexExpressionUser() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB successfully');

    // Complex expression - requires proper order of operations
    const username = 'mobileuser';
    const complexExpression = '12+5*7-3*2+8/4'; // = 12+35-6+2 = 43
    const displayName = 'Mobile Calculator User';

    // Check if user already exists
    const existingUser = await User.findOne({ username: username });
    if (existingUser) {
      console.log(`User '${username}' already exists. Deleting first...`);
      await User.deleteOne({ username: username });
    }

    // Hash the expression using bcrypt
    const expressionHash = await bcrypt.hash(complexExpression, 10);

    // Create the user
    const newUser = new User({
      username: username,
      email: `${username}@ccalc.app`,
      expressionHash: expressionHash,
      unlockExpression: complexExpression,
      expressionType: 'calculator',
      expressionUpdatedAt: new Date(),
      profile: {
        displayName: displayName
      },
      status: 'pending_device_registration',
      failedLoginAttempts: 0,
      isSuperuser: false
    });

    const savedUser = await newUser.save();
    console.log('\n✅ Complex expression user created successfully!');
    console.log('User details:');
    console.log(`  Username: ${savedUser.username}`);
    console.log(`  Display Name: ${savedUser.profile.displayName}`);
    console.log(`  Status: ${savedUser.status}`);
    console.log(`  Expression: ${savedUser.unlockExpression}`);
    console.log(`  Expected Result: 43 (12+35-6+2)`);
    console.log(`  Expression Type: ${savedUser.expressionType}`);

    console.log('\n📱 Mobile App Test Credentials:');
    console.log(`Expression: ${complexExpression}`);
    console.log('Expected Result: 43');
    console.log('Note: App must calculate this exact expression, not just the result');

    console.log('\n🧮 Expression Breakdown:');
    console.log('12 + 5*7 - 3*2 + 8/4');
    console.log('= 12 + 35 - 6 + 2');  
    console.log('= 43');

  } catch (error) {
    console.error('Error creating complex expression user:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDatabase connection closed');
  }
}

createComplexExpressionUser();
