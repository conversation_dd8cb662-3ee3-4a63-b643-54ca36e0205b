/**
 * Chat List Screen Component
 * Shows available chats - superuser for normal users, all users for superuser
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { theme } from '../utils/theme';
import { AuthService } from '../services/AuthService';
import { ChatService } from '../services/ChatService';

export interface ChatListItem {
  id: string;
  username: string;
  displayName: string;
  lastMessage?: string;
  lastMessageTime?: number;
  unreadCount: number;
  isOnline: boolean;
  userType: 'user' | 'superuser';
}

interface ChatListScreenProps {
  onSelectChat: (chatUser: ChatListItem) => void;
  onLogout: () => void;
}

export const ChatListScreen: React.FC<ChatListScreenProps> = ({ 
  onSelectChat, 
  onLogout 
}) => {
  const [chats, setChats] = useState<ChatListItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);

  const authService = AuthService.getInstance();
  const chatService = ChatService.getInstance();

  useEffect(() => {
    initializeChatList();
  }, []);

  const initializeChatList = async () => {
    try {
      setIsLoading(true);
      await loadCurrentUser();
      await loadChatList();
    } catch (error) {
      console.error('Failed to initialize chat list:', error);
      Alert.alert('Error', 'Failed to load chat list');
    } finally {
      setIsLoading(false);
    }
  };

  const loadCurrentUser = async () => {
    try {
      // Get current user info from token
      const token = authService.getToken();
      if (!token) {
        throw new Error('No authentication token');
      }

      // Decode JWT token to get user info
      try {
        const base64Url = token.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
        
        const payload = JSON.parse(jsonPayload);
        
        setCurrentUser({
          username: payload.username || 'mobileuser',
          userType: payload.type || 'user', // 'user' or 'superuser'
          displayName: payload.displayName || (payload.type === 'superuser' ? 'Administrator' : 'Mobile User'),
        });
      } catch (decodeError) {
        console.error('Error decoding token:', decodeError);
        // Fallback to default user
        setCurrentUser({
          username: 'mobileuser',
          userType: 'user',
          displayName: 'Mobile User',
        });
      }
    } catch (error) {
      console.error('Error loading current user:', error);
    }
  };

  const loadChatList = async () => {
    try {
      const result = await chatService.getChatUsers();
      
      if (result.success && result.users) {
        // Convert API response to ChatListItem format
        const chatItems: ChatListItem[] = result.users.map(user => ({
          id: user.id,
          username: user.username,
          displayName: user.displayName,
          lastMessage: 'No messages yet',
          lastMessageTime: user.lastMessageTime,
          unreadCount: user.unreadCount || 0,
          isOnline: user.isOnline || true,
          userType: user.userType,
        }));

        setChats(chatItems);
        
        // Update current user info if available
        if (result.currentUser) {
          setCurrentUser({
            username: result.currentUser.username,
            userType: result.currentUser.userType,
            displayName: result.currentUser.displayName,
          });
        }
      } else {
        console.error('Failed to load chat users:', result.error);
        // Fallback to empty list
        setChats([]);
      }
    } catch (error) {
      console.error('Error loading chat list:', error);
      // Fallback to empty list
      setChats([]);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadChatList();
    setRefreshing(false);
  };

  const formatLastMessageTime = (timestamp?: number) => {
    if (!timestamp) return '';
    
    const now = Date.now();
    const diff = now - timestamp;
    
    if (diff < 60000) return 'Now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h`;
    return `${Math.floor(diff / 86400000)}d`;
  };

  const handleChatPress = (chat: ChatListItem) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onSelectChat(chat);
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await authService.logout();
            onLogout();
          },
        },
      ]
    );
  };

  const renderChatItem = ({ item }: { item: ChatListItem }) => (
    <TouchableOpacity
      style={[
        styles.chatItem,
        item.unreadCount > 0 && styles.chatItemUnread,
      ]}
      onPress={() => handleChatPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.chatAvatar}>
        <Text style={styles.chatAvatarText}>
          {item.displayName.split(' ').map(n => n[0]).join('').toUpperCase()}
        </Text>
        {item.isOnline && <View style={styles.onlineIndicator} />}
      </View>
      
      <View style={styles.chatInfo}>
        <View style={styles.chatHeader}>
          <Text style={styles.chatDisplayName}>{item.displayName}</Text>
          <View style={styles.chatMeta}>
            <Text style={styles.chatTime}>
              {formatLastMessageTime(item.lastMessageTime)}
            </Text>
            {item.unreadCount > 0 && (
              <View style={styles.unreadBadge}>
                <Text style={styles.unreadCount}>{item.unreadCount}</Text>
              </View>
            )}
          </View>
        </View>
        
        <Text style={styles.chatLastMessage} numberOfLines={1}>
          {item.lastMessage || 'No messages yet'}
        </Text>
      </View>
    </TouchableOpacity>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading chats...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Messages</Text>
        <TouchableOpacity
          style={styles.logoutButton}
          onPress={handleLogout}
          activeOpacity={0.7}
        >
          <Text style={styles.logoutButtonText}>Logout</Text>
        </TouchableOpacity>
      </View>

      {/* Chat List */}
      <FlatList
        data={chats}
        renderItem={renderChatItem}
        keyExtractor={(item) => item.id}
        style={styles.chatList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />

      {/* Empty State */}
      {chats.length === 0 && (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No chats available</Text>
          <Text style={styles.emptySubtext}>
            {currentUser?.userType === 'superuser' 
              ? 'Users will appear here when they start conversations'
              : 'Contact the administrator to start a conversation'
            }
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.secondaryLabel,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.systemGray5,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.label,
  },
  logoutButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: theme.colors.systemRed + '20',
  },
  logoutButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.systemRed,
  },
  chatList: {
    flex: 1,
  },
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.background,
  },
  chatItemUnread: {
    backgroundColor: theme.colors.primary + '05',
  },
  chatAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    position: 'relative',
  },
  chatAvatarText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: theme.colors.systemGreen,
    borderWidth: 2,
    borderColor: theme.colors.background,
  },
  chatInfo: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  chatDisplayName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.label,
    flex: 1,
  },
  chatMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  chatTime: {
    fontSize: 12,
    color: theme.colors.secondaryLabel,
    marginRight: 8,
  },
  unreadBadge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  unreadCount: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
  },
  chatLastMessage: {
    fontSize: 14,
    color: theme.colors.secondaryLabel,
  },
  separator: {
    height: 1,
    backgroundColor: theme.colors.systemGray5,
    marginLeft: 86, // Align with chat content
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.label,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: theme.colors.secondaryLabel,
    textAlign: 'center',
    lineHeight: 20,
  },
});

// ChatListScreen is already exported above
