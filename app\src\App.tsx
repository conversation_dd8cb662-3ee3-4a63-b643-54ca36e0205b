/**
 * Main App Component
 * Handles navigation between Calculator and Chat screens based on authentication
 */

import React, { useState, useEffect } from 'react';
import { StatusBar, View, Text, TouchableOpacity, SafeAreaView, StyleSheet, FlatList } from 'react-native';

import { CalculatorScreen } from './screens/CalculatorScreen';
import { AuthService } from './services/AuthService';
import { theme } from './utils/theme';

// Simple Chat List Item Interface
interface ChatListItem {
  id: string;
  username: string;
  displayName: string;
  lastMessage?: string;
  unreadCount: number;
  isOnline: boolean;
  userType: 'user' | 'superuser';
}

// Simple Chat List Component
const SimpleChatList: React.FC<{ onLogout: () => void; onSelectChat: (chat: ChatListItem) => void }> = ({ onLogout, onSelectChat }) => {
  const [chats] = useState<ChatListItem[]>([
    {
      id: '1',
      username: 'superuser',
      displayName: 'System Administrator',
      lastMessage: 'Welcome to CCALC secure messaging',
      unreadCount: 1,
      isOnline: true,
      userType: 'superuser'
    },
    {
      id: '2',
      username: 'support',
      displayName: 'Technical Support',
      lastMessage: 'How can I help you today?',
      unreadCount: 0,
      isOnline: true,
      userType: 'user'
    },
    {
      id: '3',
      username: 'security',
      displayName: 'Security Team',
      lastMessage: 'Your device has been verified',
      unreadCount: 0,
      isOnline: false,
      userType: 'user'
    }
  ]);

  const renderChatItem = ({ item }: { item: ChatListItem }) => (
    <TouchableOpacity
      style={styles.chatItem}
      onPress={() => onSelectChat(item)}
      activeOpacity={0.7}
    >
      <View style={styles.chatItemContent}>
        <View style={styles.chatItemHeader}>
          <View style={styles.userInfo}>
            <Text style={styles.displayName}>{item.displayName}</Text>
            <View style={[styles.statusDot, { backgroundColor: item.isOnline ? '#4CAF50' : '#9E9E9E' }]} />
          </View>
          {item.unreadCount > 0 && (
            <View style={styles.unreadBadge}>
              <Text style={styles.unreadText}>{item.unreadCount}</Text>
            </View>
          )}
        </View>
        <Text style={styles.username}>@{item.username}</Text>
        {item.lastMessage && (
          <Text style={styles.lastMessage} numberOfLines={1}>
            {item.lastMessage}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.chatContainer}>
      <StatusBar barStyle="dark-content" backgroundColor={theme.colors.background} />

      {/* Header */}
      <View style={styles.chatHeader}>
        <Text style={styles.chatTitle}>🔐 CCALC Secure Chat</Text>
        <TouchableOpacity style={styles.logoutButton} onPress={onLogout}>
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>
      </View>

      {/* Chat List */}
      <FlatList
        data={chats}
        renderItem={renderChatItem}
        keyExtractor={(item) => item.id}
        style={styles.chatList}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

// Simple Chat Screen Component
const SimpleChatScreen: React.FC<{ chat: ChatListItem; onBack: () => void }> = ({ chat, onBack }) => {
  return (
    <SafeAreaView style={styles.chatContainer}>
      <StatusBar barStyle="dark-content" backgroundColor={theme.colors.background} />

      {/* Chat Header */}
      <View style={styles.chatHeader}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Text style={styles.backText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.chatTitle}>{chat.displayName}</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Chat Content */}
      <View style={styles.chatContent}>
        <Text style={styles.welcomeText}>💬 Chat with {chat.displayName}</Text>
        <Text style={styles.descriptionText}>
          This is a secure encrypted chat with {chat.displayName}.{'\n\n'}
          In the full version, you would see:
        </Text>

        <View style={styles.featureList}>
          <Text style={styles.featureItem}>• 📝 Real-time messaging</Text>
          <Text style={styles.featureItem}>• 🔒 End-to-end encryption</Text>
          <Text style={styles.featureItem}>• 📁 File sharing</Text>
          <Text style={styles.featureItem}>• 🔔 Message notifications</Text>
          <Text style={styles.featureItem}>• ✅ Read receipts</Text>
        </View>

        <Text style={styles.statusText}>
          Chat Status: 🔗 Connected{'\n'}
          Encryption: 🔐 Active{'\n'}
          User: {chat.isOnline ? '🟢 Online' : '🔴 Offline'}
        </Text>
      </View>
    </SafeAreaView>
  );
};

export default function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedChat, setSelectedChat] = useState<ChatListItem | null>(null);

  const authService = AuthService.getInstance();

  useEffect(() => {
    checkAuthenticationStatus();
  }, []);

  const checkAuthenticationStatus = async () => {
    try {
      const authenticated = await authService.isAuthenticated();
      setIsAuthenticated(authenticated);
    } catch (error) {
      console.error('Auth check error:', error);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAuthenticationSuccess = () => {
    console.log('🔄 App: handleAuthenticationSuccess called');
    setIsAuthenticated(true);
    console.log('✅ App: isAuthenticated state set to true');
  };

  const handleLogout = async () => {
    try {
      await authService.logout();
      setIsAuthenticated(false);
      setSelectedChat(null);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleSelectChat = (chat: ChatListItem) => {
    setSelectedChat(chat);
  };

  const handleBackToList = () => {
    setSelectedChat(null);
  };

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>Loading...</Text>
      </View>
    );
  }

  if (isAuthenticated) {
    if (selectedChat) {
      return <SimpleChatScreen chat={selectedChat} onBack={handleBackToList} />;
    }
    return <SimpleChatList onLogout={handleLogout} onSelectChat={handleSelectChat} />;
  }

  return (
    <CalculatorScreen onAuthenticationSuccess={handleAuthenticationSuccess} />
  );
}

const styles = StyleSheet.create({
  chatContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.primary,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  chatTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  logoutButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
  },
  logoutText: {
    color: 'white',
    fontWeight: '600',
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
  },
  backText: {
    color: 'white',
    fontWeight: '600',
  },
  placeholder: {
    width: 80,
  },
  chatList: {
    flex: 1,
  },
  chatItem: {
    backgroundColor: 'white',
    marginHorizontal: theme.spacing.md,
    marginVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chatItemContent: {
    flex: 1,
  },
  chatItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  displayName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginRight: theme.spacing.sm,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  unreadBadge: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.sm,
  },
  unreadText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  username: {
    fontSize: 14,
    color: '#666666',
    marginBottom: theme.spacing.xs,
  },
  lastMessage: {
    fontSize: 14,
    color: '#888888',
    fontStyle: 'italic',
  },
  chatContent: {
    flex: 1,
    padding: theme.spacing.lg,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    color: theme.colors.primary,
  },
  descriptionText: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    color: '#333333',
  },
  featureList: {
    backgroundColor: '#f8f9fa',
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.lg,
  },
  featureItem: {
    fontSize: 16,
    lineHeight: 28,
    color: '#333333',
    marginBottom: theme.spacing.sm,
  },
  statusText: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
    color: '#666666',
    backgroundColor: '#e8f5e8',
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  },
});
