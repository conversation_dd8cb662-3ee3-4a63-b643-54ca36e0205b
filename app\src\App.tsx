/**
 * Main App Component
 * Handles navigation between Calculator and Chat screens based on authentication
 */

import React from 'react';
import { View, Text, SafeAreaView } from 'react-native';

export default function App() {
  return (
    <SafeAreaView style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Text>🎉 CCALC App Loading Test</Text>
      <Text>Testing without CalculatorScreen</Text>
    </SafeAreaView>
  );
}
