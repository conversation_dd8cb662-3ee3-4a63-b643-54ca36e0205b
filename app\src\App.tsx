/**
 * Main App Component
 * Handles navigation between Calculator and Chat screens based on authentication
 */

import React, { useState, useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar, Platform, View, Text } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

import { CalculatorScreen } from './screens/CalculatorScreen';
import { ChatScreen } from './screens/ChatScreen';
import { ChatListScreen, ChatListItem } from './screens/ChatListScreen';
import { AuthService } from './services/AuthService';
import { theme } from './utils/theme';

type RootStackParamList = {
  Calculator: undefined;
  ChatList: undefined;
  Chat: { chatUser: ChatListItem };
};

const Stack = createStackNavigator<RootStackParamList>();

export default function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const authService = AuthService.getInstance();

  useEffect(() => {
    checkAuthenticationStatus();
  }, []);

  const checkAuthenticationStatus = async () => {
    try {
      const authenticated = await authService.isAuthenticated();
      setIsAuthenticated(authenticated);
    } catch (error) {
      console.error('Auth check error:', error);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAuthenticationSuccess = () => {
    console.log('🔄 App: handleAuthenticationSuccess called');
    setIsAuthenticated(true);
    console.log('✅ App: isAuthenticated state set to true');
  };

  const handleLogout = async () => {
    try {
      await authService.logout();
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>Loading...</Text>
      </View>
    );
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <NavigationContainer>
        <StatusBar
          barStyle={isAuthenticated ? 'dark-content' : 'light-content'}
          backgroundColor={isAuthenticated ? theme.colors.background : theme.colors.calculatorBackground}
          translucent={Platform.OS === 'android'}
        />
        <Stack.Navigator
          screenOptions={{
            headerShown: false,
            animationEnabled: true,
            cardStyleInterpolator: ({ current, layouts }) => {
              return {
                cardStyle: {
                  transform: [
                    {
                      translateX: current.progress.interpolate({
                        inputRange: [0, 1],
                        outputRange: [layouts.screen.width, 0],
                      }),
                    },
                  ],
                },
              };
            },
          }}
        >
          {isAuthenticated ? (
            <>
              <Stack.Screen name="ChatList">
                {({ navigation }) => (
                  <ChatListScreen
                    onSelectChat={(chatUser) => {
                      navigation.navigate('Chat', { chatUser });
                    }}
                    onLogout={handleLogout}
                  />
                )}
              </Stack.Screen>
              <Stack.Screen name="Chat">
                {({ route, navigation }) => (
                  <ChatScreen
                    chatUser={(route.params as any)?.chatUser}
                    onBack={() => navigation.goBack()}
                    onLogout={handleLogout}
                  />
                )}
              </Stack.Screen>
            </>
          ) : (
            <Stack.Screen name="Calculator">
              {() => <CalculatorScreen onAuthenticationSuccess={handleAuthenticationSuccess} />}
            </Stack.Screen>
          )}
        </Stack.Navigator>
      </NavigationContainer>
    </GestureHandlerRootView>
  );
}
