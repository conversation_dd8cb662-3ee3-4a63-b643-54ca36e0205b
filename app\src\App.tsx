/**
 * Main App Component
 * Handles navigation between Calculator and Chat screens based on authentication
 */

import React, { useState, useEffect } from 'react';
import { StatusBar, Platform, View, Text, TouchableOpacity, SafeAreaView, StyleSheet } from 'react-native';

import { CalculatorScreen } from './screens/CalculatorScreen';
import { AuthService } from './services/AuthService';
import { theme } from './utils/theme';

// Simple Chat Interface Component
const SimpleChatInterface: React.FC<{ onLogout: () => void }> = ({ onLogout }) => {
  return (
    <SafeAreaView style={styles.chatContainer}>
      <StatusBar barStyle="dark-content" backgroundColor={theme.colors.background} />

      {/* Header */}
      <View style={styles.chatHeader}>
        <Text style={styles.chatTitle}>🔐 CCALC Secure Chat</Text>
        <TouchableOpacity style={styles.logoutButton} onPress={onLogout}>
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>
      </View>

      {/* Chat Content */}
      <View style={styles.chatContent}>
        <Text style={styles.welcomeText}>🎉 Authentication Successful!</Text>
        <Text style={styles.descriptionText}>
          Welcome to the secure chat interface.{'\n\n'}
          You have successfully authenticated using the mathematical expression.{'\n\n'}
          In the full version, this would show:
        </Text>

        <View style={styles.featureList}>
          <Text style={styles.featureItem}>• 📱 List of available chat users</Text>
          <Text style={styles.featureItem}>• 💬 Secure messaging interface</Text>
          <Text style={styles.featureItem}>• 🔒 End-to-end encrypted conversations</Text>
          <Text style={styles.featureItem}>• 📁 File sharing capabilities</Text>
          <Text style={styles.featureItem}>• 🔔 Real-time notifications</Text>
        </View>

        <Text style={styles.statusText}>
          Status: ✅ Authenticated as Mobile User{'\n'}
          Device: 📱 Registered and Secure{'\n'}
          Connection: 🔗 Encrypted
        </Text>
      </View>
    </SafeAreaView>
  );
};

export default function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const authService = AuthService.getInstance();

  useEffect(() => {
    checkAuthenticationStatus();
  }, []);

  const checkAuthenticationStatus = async () => {
    try {
      const authenticated = await authService.isAuthenticated();
      setIsAuthenticated(authenticated);
    } catch (error) {
      console.error('Auth check error:', error);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAuthenticationSuccess = () => {
    console.log('🔄 App: handleAuthenticationSuccess called');
    setIsAuthenticated(true);
    console.log('✅ App: isAuthenticated state set to true');
  };

  const handleLogout = async () => {
    try {
      await authService.logout();
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>Loading...</Text>
      </View>
    );
  }

  if (isAuthenticated) {
    return <SimpleChatInterface onLogout={handleLogout} />;
  }

  return (
    <CalculatorScreen onAuthenticationSuccess={handleAuthenticationSuccess} />
  );
}

const styles = StyleSheet.create({
  chatContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.primary,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  chatTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  logoutButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
  },
  logoutText: {
    color: 'white',
    fontWeight: '600',
  },
  chatContent: {
    flex: 1,
    padding: theme.spacing.lg,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    color: theme.colors.primary,
  },
  descriptionText: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    color: '#333333',
  },
  featureList: {
    backgroundColor: '#f8f9fa',
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.lg,
  },
  featureItem: {
    fontSize: 16,
    lineHeight: 28,
    color: '#333333',
    marginBottom: theme.spacing.sm,
  },
  statusText: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
    color: '#666666',
    backgroundColor: '#e8f5e8',
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  },
});
