/**
 * Main App Component
 * Handles navigation between Calculator and Chat screens based on authentication
 */

import React, { useState } from 'react';
import { SafeAreaView } from 'react-native';

import { CalculatorScreen } from './screens/CalculatorScreen';
import { ChatListScreen, ChatListItem } from './screens/ChatListScreen';
import ChatScreen from './screens/EnhancedChatScreen';

type AppScreen = 'calculator' | 'chatList' | 'chat';

export default function App() {
  const [currentScreen, setCurrentScreen] = useState<AppScreen>('calculator');
  const [selectedChat, setSelectedChat] = useState<ChatListItem | undefined>(undefined);

  const handleAuthenticationSuccess = () => {
    console.log('🔄 App: handleAuthenticationSuccess called');
    setCurrentScreen('chatList');
    console.log('✅ App: Navigated to chat list');
  };

  const handleSelectChat = (chatUser: ChatListItem) => {
    setSelectedChat(chatUser);
    setCurrentScreen('chat');
  };

  const handleBackToList = () => {
    setSelectedChat(undefined);
    setCurrentScreen('chatList');
  };

  const handleLogout = () => {
    setCurrentScreen('calculator');
    setSelectedChat(undefined);
  };

  const renderScreen = () => {
    switch (currentScreen) {
      case 'calculator':
        return <CalculatorScreen onAuthenticationSuccess={handleAuthenticationSuccess} />;
      
      case 'chatList':
        return (
          <ChatListScreen 
            onSelectChat={handleSelectChat}
            onLogout={handleLogout}
          />
        );
      
      case 'chat':
        return (
          <ChatScreen 
            chatUser={selectedChat}
            onBack={handleBackToList}
            onLogout={handleLogout}
          />
        );
      
      default:
        return <CalculatorScreen onAuthenticationSuccess={handleAuthenticationSuccess} />;
    }
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      {renderScreen()}
    </SafeAreaView>
  );
}
