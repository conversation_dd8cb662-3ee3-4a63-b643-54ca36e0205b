/**
 * Main App Component
 * Handles navigation between Calculator and Chat screens based on authentication
 */

import React, { useState } from 'react';
import { Text, SafeAreaView } from 'react-native';

import { CalculatorScreen } from './screens/CalculatorScreen';

export default function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const handleAuthenticationSuccess = () => {
    setIsAuthenticated(true);
  };

  if (isAuthenticated) {
    return (
      <SafeAreaView style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#f0f0f0' }}>
        <Text style={{ fontSize: 24, fontWeight: 'bold', marginBottom: 20 }}>🎉 Authentication Successful!</Text>
        <Text style={{ fontSize: 16, textAlign: 'center', paddingHorizontal: 20 }}>
          Chat interface would load here.{'\n'}
          In the full app, this transitions to secure messaging.
        </Text>
      </SafeAreaView>
    );
  }

  return (
    <CalculatorScreen onAuthenticationSuccess={handleAuthenticationSuccess} />
  );
}
