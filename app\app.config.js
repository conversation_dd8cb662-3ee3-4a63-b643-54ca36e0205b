export default {
  expo: {
    name: "<PERSON><PERSON><PERSON>",
    slug: "ccalc-app",
    version: "1.0.0",
    sdkVersion: "53.0.0",
    orientation: "portrait",
    userInterfaceStyle: "light",
    platforms: ["ios"],
    assetBundlePatterns: [
      "**/*"
    ],
    plugins: [
      "expo-secure-store",
      "expo-av"
    ],
    ios: {
      supportsTablet: false,
      bundleIdentifier: "com.ccalc.app",
      buildNumber: "1.0.0",
      deploymentTarget: "13.4"
    },
    extra: {
      backendUrl: "http://10.254.200.174:3000",
      frontendUrl: "http://10.254.200.174:3001"
    },
    jsEngine: "hermes"
  }
};
