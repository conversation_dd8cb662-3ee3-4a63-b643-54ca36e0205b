export default {
  expo: {
    name: "<PERSON><PERSON><PERSON>",
    slug: "ccalc-app",
    version: "1.0.0",
    sdkVersion: "53.0.0",
    orientation: "portrait",
    userInterfaceStyle: "light",
    platforms: ["ios"], // iOS-only app
    assetBundlePatterns: [
      "**/*"
    ],
    plugins: [
      "expo-secure-store",
      "expo-av",
      "expo-crypto",
      "expo-document-picker",
      "expo-haptics",
      [
        "expo-build-properties",
        {
          ios: {
            deploymentTarget: "13.4"
          }
        }
      ]
    ],
    ios: {
      supportsTablet: false,
      bundleIdentifier: "com.ccalc.app",
      buildNumber: "1.0.0",
      deploymentTarget: "13.4",
      infoPlist: {
        NSBluetoothAlwaysUsageDescription: "This app uses Bluetooth to authenticate secure earbuds for voice calls.",
        NSBluetoothPeripheralUsageDescription: "This app uses Bluetooth to authenticate secure earbuds for voice calls.",
        NSMicrophoneUsageDescription: "This app needs microphone access for secure voice calls.",
        NSCameraUsageDescription: "This app needs camera access to scan documents and take photos.",
        NSPhotoLibraryUsageDescription: "This app needs photo library access to attach images to messages."
      }
    },
    extra: {
      backendUrl: "http://**************:3000",
      frontendUrl: "http://**************:3001"
    },
    jsEngine: "hermes",
    runtimeVersion: {
      policy: "sdkVersion"
    }
  }
};
