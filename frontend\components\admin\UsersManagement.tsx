import React, { useState, useCallback, useEffect } from 'react';
import { motion } from 'framer-motion';
import { withAdminAuth } from '../../contexts/AdminAuthContext';
import apiClient from '../../utils/apiClient';

interface UserDetails {
  _id: string;
  username: string;
  email: string;
  isActive: boolean;
  status: string;
  createdAt: string;
  lastLogin: string;
  devices: Device[];
  loginHistory: LoginHistory[];
  chatHistory: ChatSession[];
  voiceRecordings: VoiceRecording[];
  securityEvents: SecurityEvent[];
  mathExpression: {
    expression: string;
    type: string;
    updatedAt: string;
  };
  profile: {
    displayName: string;
  };
  isSuperuser: boolean;
}

interface Device {
  deviceId: string;
  fingerprint: string;
  deviceType: string;
  deviceModel?: string;
  os?: string;
  browser?: string;
  lastUsed: string;
  isActive: boolean;
  bleDevices: BLEDevice[];
}

interface BLEDevice {
  deviceId: string;
  deviceName: string;
  pairedAt: string;
  lastConnected: string;
  isVerified: boolean;
}

interface LoginHistory {
  timestamp: string;
  ipAddress: string;
  userAgent: string;
  deviceFingerprint: string;
  location?: {
    country: string;
    city: string;
  };
  success: boolean;
  failureReason?: string;
}

interface ChatSession {
  sessionId: string;
  startedAt: string;
  endedAt?: string;
  messageCount: number;
  voiceCallDuration: number;
  encryptionUsed: boolean;
}

interface VoiceRecording {
  recordingId: string;
  sessionId: string;
  timestamp: string;
  duration: number;
  fileSize: number;
  voiceProfile: string;
  isProcessed: boolean;
}

interface SecurityEvent {
  eventType: string;
  timestamp: string;
  details: any;
  severity: string;
  resolved: boolean;
}

const UsersManagement: React.FC = () => {
  const [users, setUsers] = useState<UserDetails[]>([]);
  const [selectedUser, setSelectedUser] = useState<UserDetails | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'devices' | 'chat' | 'voice' | 'security' | 'logs'>('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiClient.get('/api/admin/users/detailed');
      setUsers(response.data.users || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const renderUserOverview = (user: UserDetails) => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">Total Devices</h3>
          <p className="text-2xl font-bold text-gray-900">{user.devices?.length || 0}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">BLE Devices</h3>
          <p className="text-2xl font-bold text-gray-900">
            {user.devices?.reduce((acc, device) => acc + (device.bleDevices?.length || 0), 0) || 0}
          </p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">Chat Sessions</h3>
          <p className="text-2xl font-bold text-gray-900">{user.chatHistory?.length || 0}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">Voice Recordings</h3>
          <p className="text-2xl font-bold text-gray-900">{user.voiceRecordings?.length || 0}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium mb-4">User Information</h3>
          <div className="space-y-3">
            <div>
              <p className="text-sm font-medium text-gray-500">Username</p>
              <p className="text-sm">{user.username}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Display Name</p>
              <p className="text-sm">{user.profile?.displayName || 'Not set'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Email</p>
              <p className="text-sm">{user.email || 'Not set'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Status</p>
              <span className={`px-2 py-1 rounded-full text-xs ${
                user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {user.status}
              </span>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Math Expression</p>
              <p className="text-sm font-mono bg-gray-100 p-2 rounded">{user.mathExpression?.expression}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Is Superuser</p>
              <span className={`px-2 py-1 rounded-full text-xs ${
                user.isSuperuser ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
              }`}>
                {user.isSuperuser ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium mb-4">Recent Activity</h3>
          <div className="space-y-3">
            {user.loginHistory?.slice(0, 5).map((login, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div>
                  <p className="font-medium">{login.success ? 'Successful Login' : 'Failed Login'}</p>
                  <p className="text-sm text-gray-500">{login.ipAddress} • {login.location?.city}, {login.location?.country}</p>
                </div>
                <span className="text-sm text-gray-500">
                  {new Date(login.timestamp).toLocaleString()}
                </span>
              </div>
            ))}
            {(!user.loginHistory || user.loginHistory.length === 0) && (
              <p className="text-gray-500 text-center py-4">No login history available</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const renderDevicesTab = (user: UserDetails) => (
    <div className="space-y-6">
      {user.devices?.map((device, index) => (
        <div key={index} className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Device {index + 1}</h3>
            <span className={`px-3 py-1 rounded-full text-sm ${
              device.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {device.isActive ? 'Active' : 'Inactive'}
            </span>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Type</p>
              <p className="text-sm">{device.deviceType}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">OS</p>
              <p className="text-sm">{device.os || 'Unknown'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Browser</p>
              <p className="text-sm">{device.browser || 'Unknown'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Last Used</p>
              <p className="text-sm">{new Date(device.lastUsed).toLocaleString()}</p>
            </div>
          </div>

          {device.bleDevices && device.bleDevices.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">BLE Devices ({device.bleDevices.length})</h4>
              <div className="space-y-2">
                {device.bleDevices.map((ble, bleIndex) => (
                  <div key={bleIndex} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <div>
                      <p className="font-medium">{ble.deviceName}</p>
                      <p className="text-sm text-gray-500">ID: {ble.deviceId}</p>
                    </div>
                    <div className="text-right">
                      <span className={`px-2 py-1 rounded text-xs ${
                        ble.isVerified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {ble.isVerified ? 'Verified' : 'Unverified'}
                      </span>
                      <p className="text-xs text-gray-500 mt-1">
                        Last: {new Date(ble.lastConnected).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ))}
      {(!user.devices || user.devices.length === 0) && (
        <div className="text-center py-8">
          <p className="text-gray-500">No devices registered</p>
        </div>
      )}
    </div>
  );

  const renderChatTab = (user: UserDetails) => (
    <div className="space-y-4">
      {user.chatHistory?.map((session, index) => (
        <div key={index} className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Session {session.sessionId}</h3>
            <span className={`px-3 py-1 rounded-full text-sm ${
              session.encryptionUsed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {session.encryptionUsed ? 'Encrypted' : 'Unencrypted'}
            </span>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Started</p>
              <p className="text-sm">{new Date(session.startedAt).toLocaleString()}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Ended</p>
              <p className="text-sm">{session.endedAt ? new Date(session.endedAt).toLocaleString() : 'Ongoing'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Messages</p>
              <p className="text-sm">{session.messageCount}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Voice Duration</p>
              <p className="text-sm">{formatDuration(session.voiceCallDuration)}</p>
            </div>
          </div>
        </div>
      ))}
      {(!user.chatHistory || user.chatHistory.length === 0) && (
        <div className="text-center py-8">
          <p className="text-gray-500">No chat history available</p>
        </div>
      )}
    </div>
  );

  const renderVoiceTab = (user: UserDetails) => (
    <div className="space-y-4">
      {user.voiceRecordings?.map((recording, index) => (
        <div key={index} className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Recording {recording.recordingId}</h3>
            <span className={`px-3 py-1 rounded-full text-sm ${
              recording.isProcessed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
            }`}>
              {recording.isProcessed ? 'Processed' : 'Processing'}
            </span>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Session</p>
              <p className="text-sm">{recording.sessionId}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Duration</p>
              <p className="text-sm">{formatDuration(recording.duration)}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">File Size</p>
              <p className="text-sm">{formatFileSize(recording.fileSize)}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Voice Profile</p>
              <p className="text-sm">{recording.voiceProfile}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Timestamp</p>
              <p className="text-sm">{new Date(recording.timestamp).toLocaleString()}</p>
            </div>
          </div>
        </div>
      ))}
      {(!user.voiceRecordings || user.voiceRecordings.length === 0) && (
        <div className="text-center py-8">
          <p className="text-gray-500">No voice recordings available</p>
        </div>
      )}
    </div>
  );

  const renderSecurityTab = (user: UserDetails) => (
    <div className="space-y-4">
      {user.securityEvents?.map((event, index) => (
        <div key={index} className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">{event.eventType.replace('_', ' ').toUpperCase()}</h3>
            <div className="flex items-center space-x-2">
              <span className={`px-3 py-1 rounded-full text-sm ${
                event.severity === 'critical' ? 'bg-red-100 text-red-800' :
                event.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                event.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-green-100 text-green-800'
              }`}>
                {event.severity.toUpperCase()}
              </span>
              <span className={`px-3 py-1 rounded-full text-sm ${
                event.resolved ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {event.resolved ? 'Resolved' : 'Open'}
              </span>
            </div>
          </div>
          
          <div className="mb-4">
            <p className="text-sm font-medium text-gray-500 mb-2">Details</p>
            <pre className="text-sm bg-gray-50 p-3 rounded overflow-x-auto">
              {JSON.stringify(event.details, null, 2)}
            </pre>
          </div>
          
          <p className="text-sm text-gray-500">
            {new Date(event.timestamp).toLocaleString()}
          </p>
        </div>
      ))}
      {(!user.securityEvents || user.securityEvents.length === 0) && (
        <div className="text-center py-8">
          <p className="text-gray-500">No security events recorded</p>
        </div>
      )}
    </div>
  );

  const renderLogsTab = (user: UserDetails) => (
    <div className="space-y-4">
      {user.loginHistory?.map((login, index) => (
        <div key={index} className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Login Attempt</h3>
            <span className={`px-3 py-1 rounded-full text-sm ${
              login.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {login.success ? 'Success' : 'Failed'}
            </span>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">IP Address</p>
              <p className="text-sm">{login.ipAddress}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Location</p>
              <p className="text-sm">{login.location ? `${login.location.city}, ${login.location.country}` : 'Unknown'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Timestamp</p>
              <p className="text-sm">{new Date(login.timestamp).toLocaleString()}</p>
            </div>
          </div>
          
          {!login.success && login.failureReason && (
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Failure Reason</p>
              <p className="text-sm text-red-600">{login.failureReason}</p>
            </div>
          )}
          
          <div className="mt-4">
            <p className="text-sm font-medium text-gray-500">User Agent</p>
            <p className="text-xs text-gray-600 break-all">{login.userAgent}</p>
          </div>
        </div>
      ))}
      {(!user.loginHistory || user.loginHistory.length === 0) && (
        <div className="text-center py-8">
          <p className="text-gray-500">No login history available</p>
        </div>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">User Management</h1>
          <p className="text-gray-600">Comprehensive user monitoring and management</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Users List */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-semibold">Users ({users.length})</h2>
              </div>
              <div className="max-h-96 overflow-y-auto">
                {loading ? (
                  <div className="p-6 text-center">Loading...</div>
                ) : error ? (
                  <div className="p-6 text-center text-red-600">Error: {error}</div>
                ) : users.length === 0 ? (
                  <div className="p-6 text-center text-gray-500">No users found</div>
                ) : (
                  users.map((user) => (
                    <div
                      key={user._id}
                      onClick={() => setSelectedUser(user)}
                      className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                        selectedUser?._id === user._id ? 'bg-blue-50 border-blue-200' : ''
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-medium">{user.username}</h3>
                          <p className="text-sm text-gray-500">{user.email || 'No email'}</p>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {user.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* User Details */}
          <div className="lg:col-span-2">
            {selectedUser ? (
              <div className="bg-white rounded-lg shadow">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-xl font-semibold">{selectedUser.username}</h2>
                  <p className="text-gray-600">{selectedUser.email || 'No email set'}</p>
                </div>

                {/* Tabs */}
                <div className="border-b border-gray-200">
                  <nav className="flex space-x-8 px-6">
                    {[
                      { key: 'overview', label: 'Overview' },
                      { key: 'devices', label: 'Devices' },
                      { key: 'chat', label: 'Chat History' },
                      { key: 'voice', label: 'Voice Recordings' },
                      { key: 'security', label: 'Security Events' },
                      { key: 'logs', label: 'Login Logs' }
                    ].map((tab) => (
                      <button
                        key={tab.key}
                        onClick={() => setActiveTab(tab.key as any)}
                        className={`py-4 px-1 border-b-2 font-medium text-sm ${
                          activeTab === tab.key
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        {tab.label}
                      </button>
                    ))}
                  </nav>
                </div>

                {/* Tab Content */}
                <div className="p-6">
                  {activeTab === 'overview' && renderUserOverview(selectedUser)}
                  {activeTab === 'devices' && renderDevicesTab(selectedUser)}
                  {activeTab === 'chat' && renderChatTab(selectedUser)}
                  {activeTab === 'voice' && renderVoiceTab(selectedUser)}
                  {activeTab === 'security' && renderSecurityTab(selectedUser)}
                  {activeTab === 'logs' && renderLogsTab(selectedUser)}
                </div>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow p-12 text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select a user</h3>
                <p className="text-gray-500">Choose a user from the list to view detailed information</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default withAdminAuth(UsersManagement);
