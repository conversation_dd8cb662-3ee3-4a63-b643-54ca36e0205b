import React, { useState, useCallback, useEffect } from 'react';
import { motion } from 'framer-motion';
import { withAdminAuth } from '../../contexts/AdminAuthContext';
import apiClient from '../../utils/apiClient';
import {
  Add as PlusIcon,
  Edit as PencilIcon,
  Delete as TrashIcon,
  Visibility as EyeIcon,
  Check as CheckIcon,
  Close as XMarkIcon,
  Warning as ExclamationTriangleIcon,
  PersonAdd as UserPlusIcon,
  People as UsersIcon
} from '@mui/icons-material';

interface UserDetails {
  _id: string;
  username: string;
  email: string;
  isActive: boolean;
  status: string;
  createdAt: string;
  lastLogin: string;
  devices: Device[];
  loginHistory: LoginHistory[];
  chatHistory: ChatSession[];
  voiceRecordings: VoiceRecording[];
  securityEvents: SecurityEvent[];
  mathExpression: {
    expression: string;
    type: string;
    updatedAt: string;
  };
  profile: {
    displayName: string;
  };
  isSuperuser: boolean;
}

interface Device {
  deviceId: string;
  fingerprint: string;
  deviceType: string;
  deviceModel?: string;
  os?: string;
  browser?: string;
  lastUsed: string;
  isActive: boolean;
  bleDevices: BLEDevice[];
}

interface BLEDevice {
  deviceId: string;
  deviceName: string;
  pairedAt: string;
  lastConnected: string;
  isVerified: boolean;
}

interface LoginHistory {
  timestamp: string;
  ipAddress: string;
  userAgent: string;
  deviceFingerprint: string;
  location?: {
    country: string;
    city: string;
  };
  success: boolean;
  failureReason?: string;
}

interface ChatSession {
  sessionId: string;
  startedAt: string;
  endedAt?: string;
  messageCount: number;
  voiceCallDuration: number;
  encryptionUsed: boolean;
}

interface VoiceRecording {
  recordingId: string;
  sessionId: string;
  timestamp: string;
  duration: number;
  fileSize: number;
  voiceProfile: string;
  isProcessed: boolean;
}

interface SecurityEvent {
  eventType: string;
  timestamp: string;
  details: any;
  severity: string;
  resolved: boolean;
}

interface CreateUserForm {
  username: string;
  email: string;
  expression: string;
  displayName: string;
  isSuperuser: boolean;
}

interface EditUserForm {
  displayName: string;
  expression: string;
  isSuperuser: boolean;
  status: string;
}

interface PasswordConfirmation {
  password: string;
}

const UsersManagement: React.FC = () => {
  const [users, setUsers] = useState<UserDetails[]>([]);
  const [selectedUser, setSelectedUser] = useState<UserDetails | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'devices' | 'chat' | 'voice' | 'security' | 'logs'>('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // CRUD Modal States
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingUser, setEditingUser] = useState<UserDetails | null>(null);

  // Form States
  const [createForm, setCreateForm] = useState<CreateUserForm>({
    username: '',
    email: '',
    expression: '',
    displayName: '',
    isSuperuser: false
  });
  const [editForm, setEditForm] = useState<EditUserForm>({
    displayName: '',
    expression: '',
    isSuperuser: false,
    status: 'pending_device_registration'
  });

  // Selection and Bulk Operations
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Password Confirmation
  const [passwordConfirm, setPasswordConfirm] = useState<PasswordConfirmation>({
    password: ''
  });

  // Loading States
  const [submitting, setSubmitting] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiClient.get('/api/admin/users/detailed');
      setUsers(response.data.users || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // CRUD Operations
  const handleCreateUser = async () => {
    try {
      setSubmitting(true);
      setFormErrors({});

      // Validate form
      const errors: Record<string, string> = {};
      if (!createForm.username.trim()) errors.username = 'Username is required';
      if (!createForm.expression.trim()) errors.expression = 'Expression is required';
      if (!createForm.displayName.trim()) errors.displayName = 'Display name is required';

      if (Object.keys(errors).length > 0) {
        setFormErrors(errors);
        return;
      }

      // Prepare the request payload
      const payload = {
        username: createForm.username.trim(),
        email: createForm.email.trim() || undefined, // Only include if provided
        expression: createForm.expression.trim(),
        displayName: createForm.displayName.trim(),
        isSuperuser: createForm.isSuperuser
      };

      console.log('Creating user with payload:', payload);

      const response = await apiClient.post('/api/admin/users', payload);
      console.log('User created successfully:', response.data);

      // Reset form and close modal
      setCreateForm({
        username: '',
        email: '',
        expression: '',
        displayName: '',
        isSuperuser: false
      });
      setShowCreateModal(false);

      // Refresh users list
      await fetchUsers();

    } catch (error: any) {
      console.error('Create user error:', error);
      console.error('Error response:', error.response?.data);
      setFormErrors({
        submit: error.response?.data?.error || error.message || 'Failed to create user'
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditUser = async () => {
    if (!editingUser) return;

    try {
      setSubmitting(true);
      setFormErrors({});

      // Validate form
      const errors: Record<string, string> = {};
      if (!editForm.displayName.trim()) errors.displayName = 'Display name is required';
      if (!editForm.expression.trim()) errors.expression = 'Expression is required';

      if (Object.keys(errors).length > 0) {
        setFormErrors(errors);
        return;
      }

      // Prepare the request payload
      const payload = {
        displayName: editForm.displayName.trim(),
        expression: editForm.expression.trim(),
        isSuperuser: editForm.isSuperuser,
        status: editForm.status
      };

      console.log('Updating user with payload:', payload);

      const response = await apiClient.put(`/api/admin/users/${editingUser._id}`, payload);
      console.log('User updated successfully:', response.data);

      // Close modal and refresh
      setShowEditModal(false);
      setEditingUser(null);
      await fetchUsers();

    } catch (error: any) {
      console.error('Update user error:', error);
      console.error('Error response:', error.response?.data);
      setFormErrors({
        submit: error.response?.data?.error || error.message || 'Failed to update user'
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteUsers = async () => {
    try {
      setSubmitting(true);
      setFormErrors({});

      // Verify admin password first
      if (!passwordConfirm.password) {
        setFormErrors({ password: 'Admin password is required' });
        return;
      }

      // TODO: Add password verification endpoint call here
      // For now, we'll proceed with deletion

      const usersToDelete = selectedUserIds.length > 0 ? selectedUserIds :
                           selectedUser ? [selectedUser._id] : [];

      if (usersToDelete.length === 0) {
        setFormErrors({ submit: 'No users selected for deletion' });
        return;
      }

      // Delete users one by one
      for (const userId of usersToDelete) {
        await apiClient.delete(`/api/admin/users/${userId}`);
      }

      // Reset states and refresh
      setSelectedUserIds([]);
      setSelectAll(false);
      setShowDeleteModal(false);
      setPasswordConfirm({ password: '' });
      await fetchUsers();

    } catch (error: any) {
      setFormErrors({
        submit: error.response?.data?.error || 'Failed to delete users'
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Selection Management
  const handleSelectUser = (userId: string) => {
    setSelectedUserIds(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedUserIds([]);
    } else {
      setSelectedUserIds(users.map(user => user._id));
    }
    setSelectAll(!selectAll);
  };

  // Modal Management
  const openCreateModal = () => {
    setCreateForm({
      username: '',
      email: '',
      expression: '',
      displayName: '',
      isSuperuser: false
    });
    setFormErrors({});
    setShowCreateModal(true);
  };

  const openEditModal = (user: UserDetails) => {
    setEditingUser(user);
    setEditForm({
      displayName: user.profile?.displayName || '',
      expression: user.mathExpression?.expression || '',
      isSuperuser: user.isSuperuser || false,
      status: user.status
    });
    setFormErrors({});
    setShowEditModal(true);
  };

  const openDeleteModal = () => {
    setPasswordConfirm({ password: '' });
    setFormErrors({});
    setShowDeleteModal(true);
  };

  const closeModals = () => {
    setShowCreateModal(false);
    setShowEditModal(false);
    setShowDeleteModal(false);
    setEditingUser(null);
    setFormErrors({});
    setPasswordConfirm({ password: '' });
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const renderUserOverview = (user: UserDetails) => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">Total Devices</h3>
          <p className="text-2xl font-bold text-gray-900">{user.devices?.length || 0}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">BLE Devices</h3>
          <p className="text-2xl font-bold text-gray-900">
            {user.devices?.reduce((acc, device) => acc + (device.bleDevices?.length || 0), 0) || 0}
          </p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">Chat Sessions</h3>
          <p className="text-2xl font-bold text-gray-900">{user.chatHistory?.length || 0}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">Voice Recordings</h3>
          <p className="text-2xl font-bold text-gray-900">{user.voiceRecordings?.length || 0}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium mb-4">User Information</h3>
          <div className="space-y-3">
            <div>
              <p className="text-sm font-medium text-gray-500">Username</p>
              <p className="text-sm">{user.username}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Display Name</p>
              <p className="text-sm">{user.profile?.displayName || 'Not set'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Email</p>
              <p className="text-sm">{user.email || 'Not set'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Status</p>
              <span className={`px-2 py-1 rounded-full text-xs ${
                user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {user.status}
              </span>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Math Expression</p>
              <p className="text-sm font-mono bg-gray-100 p-2 rounded">{user.mathExpression?.expression}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Is Superuser</p>
              <span className={`px-2 py-1 rounded-full text-xs ${
                user.isSuperuser ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
              }`}>
                {user.isSuperuser ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium mb-4">Recent Activity</h3>
          <div className="space-y-3">
            {user.loginHistory?.slice(0, 5).map((login, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div>
                  <p className="font-medium">{login.success ? 'Successful Login' : 'Failed Login'}</p>
                  <p className="text-sm text-gray-500">{login.ipAddress} • {login.location?.city}, {login.location?.country}</p>
                </div>
                <span className="text-sm text-gray-500">
                  {new Date(login.timestamp).toLocaleString()}
                </span>
              </div>
            ))}
            {(!user.loginHistory || user.loginHistory.length === 0) && (
              <p className="text-gray-500 text-center py-4">No login history available</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const renderDevicesTab = (user: UserDetails) => (
    <div className="space-y-6">
      {user.devices?.map((device, index) => (
        <div key={index} className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Device {index + 1}</h3>
            <span className={`px-3 py-1 rounded-full text-sm ${
              device.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {device.isActive ? 'Active' : 'Inactive'}
            </span>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Type</p>
              <p className="text-sm">{device.deviceType}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">OS</p>
              <p className="text-sm">{device.os || 'Unknown'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Browser</p>
              <p className="text-sm">{device.browser || 'Unknown'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Last Used</p>
              <p className="text-sm">{new Date(device.lastUsed).toLocaleString()}</p>
            </div>
          </div>

          {device.bleDevices && device.bleDevices.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">BLE Devices ({device.bleDevices.length})</h4>
              <div className="space-y-2">
                {device.bleDevices.map((ble, bleIndex) => (
                  <div key={bleIndex} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <div>
                      <p className="font-medium">{ble.deviceName}</p>
                      <p className="text-sm text-gray-500">ID: {ble.deviceId}</p>
                    </div>
                    <div className="text-right">
                      <span className={`px-2 py-1 rounded text-xs ${
                        ble.isVerified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {ble.isVerified ? 'Verified' : 'Unverified'}
                      </span>
                      <p className="text-xs text-gray-500 mt-1">
                        Last: {new Date(ble.lastConnected).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ))}
      {(!user.devices || user.devices.length === 0) && (
        <div className="text-center py-8">
          <p className="text-gray-500">No devices registered</p>
        </div>
      )}
    </div>
  );

  const renderChatTab = (user: UserDetails) => (
    <div className="space-y-4">
      {user.chatHistory?.map((session, index) => (
        <div key={index} className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Session {session.sessionId}</h3>
            <span className={`px-3 py-1 rounded-full text-sm ${
              session.encryptionUsed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {session.encryptionUsed ? 'Encrypted' : 'Unencrypted'}
            </span>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Started</p>
              <p className="text-sm">{new Date(session.startedAt).toLocaleString()}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Ended</p>
              <p className="text-sm">{session.endedAt ? new Date(session.endedAt).toLocaleString() : 'Ongoing'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Messages</p>
              <p className="text-sm">{session.messageCount}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Voice Duration</p>
              <p className="text-sm">{formatDuration(session.voiceCallDuration)}</p>
            </div>
          </div>
        </div>
      ))}
      {(!user.chatHistory || user.chatHistory.length === 0) && (
        <div className="text-center py-8">
          <p className="text-gray-500">No chat history available</p>
        </div>
      )}
    </div>
  );

  const renderVoiceTab = (user: UserDetails) => (
    <div className="space-y-4">
      {user.voiceRecordings?.map((recording, index) => (
        <div key={index} className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Recording {recording.recordingId}</h3>
            <span className={`px-3 py-1 rounded-full text-sm ${
              recording.isProcessed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
            }`}>
              {recording.isProcessed ? 'Processed' : 'Processing'}
            </span>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Session</p>
              <p className="text-sm">{recording.sessionId}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Duration</p>
              <p className="text-sm">{formatDuration(recording.duration)}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">File Size</p>
              <p className="text-sm">{formatFileSize(recording.fileSize)}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Voice Profile</p>
              <p className="text-sm">{recording.voiceProfile}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Timestamp</p>
              <p className="text-sm">{new Date(recording.timestamp).toLocaleString()}</p>
            </div>
          </div>
        </div>
      ))}
      {(!user.voiceRecordings || user.voiceRecordings.length === 0) && (
        <div className="text-center py-8">
          <p className="text-gray-500">No voice recordings available</p>
        </div>
      )}
    </div>
  );

  const renderSecurityTab = (user: UserDetails) => (
    <div className="space-y-4">
      {user.securityEvents?.map((event, index) => (
        <div key={index} className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">{event.eventType.replace('_', ' ').toUpperCase()}</h3>
            <div className="flex items-center space-x-2">
              <span className={`px-3 py-1 rounded-full text-sm ${
                event.severity === 'critical' ? 'bg-red-100 text-red-800' :
                event.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                event.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-green-100 text-green-800'
              }`}>
                {event.severity.toUpperCase()}
              </span>
              <span className={`px-3 py-1 rounded-full text-sm ${
                event.resolved ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {event.resolved ? 'Resolved' : 'Open'}
              </span>
            </div>
          </div>
          
          <div className="mb-4">
            <p className="text-sm font-medium text-gray-500 mb-2">Details</p>
            <pre className="text-sm bg-gray-50 p-3 rounded overflow-x-auto">
              {JSON.stringify(event.details, null, 2)}
            </pre>
          </div>
          
          <p className="text-sm text-gray-500">
            {new Date(event.timestamp).toLocaleString()}
          </p>
        </div>
      ))}
      {(!user.securityEvents || user.securityEvents.length === 0) && (
        <div className="text-center py-8">
          <p className="text-gray-500">No security events recorded</p>
        </div>
      )}
    </div>
  );

  const renderLogsTab = (user: UserDetails) => (
    <div className="space-y-4">
      {user.loginHistory?.map((login, index) => (
        <div key={index} className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Login Attempt</h3>
            <span className={`px-3 py-1 rounded-full text-sm ${
              login.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {login.success ? 'Success' : 'Failed'}
            </span>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">IP Address</p>
              <p className="text-sm">{login.ipAddress}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Location</p>
              <p className="text-sm">{login.location ? `${login.location.city}, ${login.location.country}` : 'Unknown'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Timestamp</p>
              <p className="text-sm">{new Date(login.timestamp).toLocaleString()}</p>
            </div>
          </div>
          
          {!login.success && login.failureReason && (
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Failure Reason</p>
              <p className="text-sm text-red-600">{login.failureReason}</p>
            </div>
          )}
          
          <div className="mt-4">
            <p className="text-sm font-medium text-gray-500">User Agent</p>
            <p className="text-xs text-gray-600 break-all">{login.userAgent}</p>
          </div>
        </div>
      ))}
      {(!user.loginHistory || user.loginHistory.length === 0) && (
        <div className="text-center py-8">
          <p className="text-gray-500">No login history available</p>
        </div>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">User Management</h1>
              <p className="text-gray-600">Comprehensive user monitoring and management</p>
            </div>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <button
                onClick={openCreateModal}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center space-x-2 transition-colors"
              >
                <UserPlusIcon className="w-5 h-5" />
                <span>Add User</span>
              </button>
              {selectedUserIds.length > 0 && (
                <button
                  onClick={openDeleteModal}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center justify-center space-x-2 transition-colors"
                >
                  <TrashIcon className="w-5 h-5" />
                  <span className="hidden sm:inline">Delete Selected ({selectedUserIds.length})</span>
                  <span className="sm:hidden">Delete ({selectedUserIds.length})</span>
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Users List */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow">
              <div className="p-6 border-b border-gray-200">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-semibold">Users ({users.length})</h2>
                  {users.length > 0 && (
                    <label className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectAll}
                        onChange={handleSelectAll}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-600">Select All</span>
                    </label>
                  )}
                </div>
              </div>
              <div className="max-h-96 overflow-y-auto">
                {loading ? (
                  <div className="p-6 text-center">Loading...</div>
                ) : error ? (
                  <div className="p-6 text-center text-red-600">Error: {error}</div>
                ) : users.length === 0 ? (
                  <div className="p-6 text-center text-gray-500">No users found</div>
                ) : (
                  users.map((user) => (
                    <div
                      key={user._id}
                      className={`p-4 border-b border-gray-100 hover:bg-gray-50 ${
                        selectedUser?._id === user._id ? 'bg-blue-50 border-blue-200' : ''
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <input
                          type="checkbox"
                          checked={selectedUserIds.includes(user._id)}
                          onChange={() => handleSelectUser(user._id)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mt-1 flex-shrink-0"
                          onClick={(e) => e.stopPropagation()}
                        />
                        <div
                          className="flex-1 min-w-0 cursor-pointer"
                          onClick={() => setSelectedUser(user)}
                        >
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div className="min-w-0 flex-1">
                              <h3 className="font-medium truncate">{user.username}</h3>
                              <p className="text-sm text-gray-500 truncate">{user.email || 'No email'}</p>
                              <p className="text-xs text-gray-400 truncate">{user.profile?.displayName}</p>
                            </div>
                            <div className="flex items-center space-x-2 mt-2 sm:mt-0 flex-shrink-0">
                              <span className={`px-2 py-1 rounded-full text-xs whitespace-nowrap ${
                                user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                              }`}>
                                {user.isActive ? 'Active' : 'Inactive'}
                              </span>
                              {user.isSuperuser && (
                                <span className="px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800 whitespace-nowrap">
                                  Superuser
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex space-x-1 flex-shrink-0">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedUser(user);
                            }}
                            className="p-1.5 text-gray-400 hover:text-blue-600 transition-colors rounded hover:bg-blue-50"
                            title="View Details"
                          >
                            <EyeIcon className="w-3.5 h-3.5" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              openEditModal(user);
                            }}
                            className="p-1.5 text-gray-400 hover:text-green-600 transition-colors rounded hover:bg-green-50"
                            title="Edit User"
                          >
                            <PencilIcon className="w-3.5 h-3.5" />
                          </button>
                          {!user.isSuperuser && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedUser(user);
                                openDeleteModal();
                              }}
                              className="p-1.5 text-gray-400 hover:text-red-600 transition-colors rounded hover:bg-red-50"
                              title="Delete User"
                            >
                              <TrashIcon className="w-3.5 h-3.5" />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* User Details */}
          <div className="lg:col-span-2">
            {selectedUser ? (
              <div className="bg-white rounded-lg shadow">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-xl font-semibold">{selectedUser.username}</h2>
                  <p className="text-gray-600">{selectedUser.email || 'No email set'}</p>
                </div>

                {/* Tabs */}
                <div className="border-b border-gray-200">
                  <nav className="flex space-x-8 px-6">
                    {[
                      { key: 'overview', label: 'Overview' },
                      { key: 'devices', label: 'Devices' },
                      { key: 'chat', label: 'Chat History' },
                      { key: 'voice', label: 'Voice Recordings' },
                      { key: 'security', label: 'Security Events' },
                      { key: 'logs', label: 'Login Logs' }
                    ].map((tab) => (
                      <button
                        key={tab.key}
                        onClick={() => setActiveTab(tab.key as any)}
                        className={`py-4 px-1 border-b-2 font-medium text-sm ${
                          activeTab === tab.key
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        {tab.label}
                      </button>
                    ))}
                  </nav>
                </div>

                {/* Tab Content */}
                <div className="p-6">
                  {activeTab === 'overview' && renderUserOverview(selectedUser)}
                  {activeTab === 'devices' && renderDevicesTab(selectedUser)}
                  {activeTab === 'chat' && renderChatTab(selectedUser)}
                  {activeTab === 'voice' && renderVoiceTab(selectedUser)}
                  {activeTab === 'security' && renderSecurityTab(selectedUser)}
                  {activeTab === 'logs' && renderLogsTab(selectedUser)}
                </div>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow p-12 text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select a user</h3>
                <p className="text-gray-500">Choose a user from the list to view detailed information</p>
              </div>
            )}
          </div>
        </div>

        {/* Create User Modal */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Create New User</h3>
                <button
                  onClick={closeModals}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="w-6 h-6" />
                </button>
              </div>

              <form onSubmit={(e) => { e.preventDefault(); handleCreateUser(); }}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Username *
                    </label>
                    <input
                      type="text"
                      value={createForm.username}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, username: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter username"
                    />
                    {formErrors.username && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.username}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email
                    </label>
                    <input
                      type="email"
                      value={createForm.email}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, email: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter email (optional)"
                    />
                    {formErrors.email && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.email}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Display Name *
                    </label>
                    <input
                      type="text"
                      value={createForm.displayName}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, displayName: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter display name"
                    />
                    {formErrors.displayName && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.displayName}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Math Expression *
                    </label>
                    <input
                      type="text"
                      value={createForm.expression}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, expression: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., 2+3*4"
                    />
                    {formErrors.expression && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.expression}</p>
                    )}
                  </div>

                  <div>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={createForm.isSuperuser}
                        onChange={(e) => setCreateForm(prev => ({ ...prev, isSuperuser: e.target.checked }))}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">Make this user a superuser</span>
                    </label>
                  </div>

                  {formErrors.submit && (
                    <div className="text-red-500 text-sm">{formErrors.submit}</div>
                  )}
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    onClick={closeModals}
                    className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={submitting}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    {submitting ? 'Creating...' : 'Create User'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Edit User Modal */}
        {showEditModal && editingUser && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Edit User: {editingUser.username}</h3>
                <button
                  onClick={closeModals}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="w-6 h-6" />
                </button>
              </div>

              <form onSubmit={(e) => { e.preventDefault(); handleEditUser(); }}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Display Name *
                    </label>
                    <input
                      type="text"
                      value={editForm.displayName}
                      onChange={(e) => setEditForm(prev => ({ ...prev, displayName: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter display name"
                    />
                    {formErrors.displayName && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.displayName}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Math Expression *
                    </label>
                    <input
                      type="text"
                      value={editForm.expression}
                      onChange={(e) => setEditForm(prev => ({ ...prev, expression: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., 2+3*4"
                    />
                    {formErrors.expression && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.expression}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Status
                    </label>
                    <select
                      value={editForm.status}
                      onChange={(e) => setEditForm(prev => ({ ...prev, status: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="locked">Locked</option>
                      <option value="pending_device_registration">Pending Device Registration</option>
                    </select>
                  </div>

                  {!editingUser.isSuperuser && (
                    <div>
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={editForm.isSuperuser}
                          onChange={(e) => setEditForm(prev => ({ ...prev, isSuperuser: e.target.checked }))}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700">Make this user a superuser</span>
                      </label>
                    </div>
                  )}

                  {formErrors.submit && (
                    <div className="text-red-500 text-sm">{formErrors.submit}</div>
                  )}
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    onClick={closeModals}
                    className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={submitting}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                  >
                    {submitting ? 'Updating...' : 'Update User'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
              <div className="flex items-center mb-4">
                <ExclamationTriangleIcon className="w-8 h-8 text-red-500 mr-3" />
                <h3 className="text-lg font-semibold">Confirm Deletion</h3>
              </div>

              <div className="mb-4">
                <p className="text-gray-600 mb-2">
                  {selectedUserIds.length > 0
                    ? `Are you sure you want to delete ${selectedUserIds.length} selected user(s)?`
                    : selectedUser
                      ? `Are you sure you want to delete user "${selectedUser.username}"?`
                      : 'Are you sure you want to delete this user?'
                  }
                </p>
                <p className="text-sm text-red-600">This action cannot be undone.</p>
              </div>

              <form onSubmit={(e) => { e.preventDefault(); handleDeleteUsers(); }}>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Admin Password *
                  </label>
                  <input
                    type="password"
                    value={passwordConfirm.password}
                    onChange={(e) => setPasswordConfirm({ password: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                    placeholder="Enter your admin password"
                  />
                  {formErrors.password && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.password}</p>
                  )}
                </div>

                {formErrors.submit && (
                  <div className="text-red-500 text-sm mb-4">{formErrors.submit}</div>
                )}

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={closeModals}
                    className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={submitting}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                  >
                    {submitting ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default withAdminAuth(UsersManagement);
