# 📱 iPhone Testing from Windows VS Code Guide

## 🎯 Current Setup Status: READY FOR iPhone TESTING

Your CCALC React Native app is configured to connect from iPhone to your Windows development machine.

### 📋 **Prerequisites**
- ✅ Your iPhone and Windows PC on same WiFi network
- ✅ Backend running on Windows (port 3000)
- ✅ Expo CLI or React Native CLI
- ✅ Expo Go app on iPhone (easiest method)

### 🔧 **Setup Methods**

## **Method 1: Expo Go (Recommended - Easiest)**

### 1. Install Expo CLI globally
```bash
npm install -g @expo/cli
```

### 2. Install Expo dependencies
```bash
cd app
npm install expo
npx expo install
```

### 3. Start Expo development server
```bash
npx expo start
```

### 4. Test on iPhone
- Install "Expo Go" app from App Store
- Scan QR code from Expo dev server
- App will load directly on your iPhone

## **Method 2: React Native CLI (Advanced)**

### 1. Start Metro bundler
```bash
cd app
npm start
```

### 2. Build for iOS (requires Mac with Xcode)
```bash
npm run ios
```

## **Method 3: Physical Device Testing (No Mac Required)**

### 1. Use React Native debugger
```bash
cd app
npm start -- --reset-cache
```

### 2. Connect via network
- Open app on device
- Shake device to open debug menu
- Configure bundle URL: `**************:8081`

---

## 🌐 **Network Configuration**

### **Your Windows Machine IPs:**
- Primary Network: `**************` ✅ (configured)
- Secondary: `*************`
- Secondary: `*************`
- Docker: `************`

### **Service URLs from iPhone:**
- Backend API: `http://**************:3000`
- Admin Frontend: `http://**************:3001`
- React Native Metro: `http://**************:8081`

---

## 🚀 **Quick Start Commands**

### From VS Code Terminal (in project root):

```bash
# 1. Start all services
docker-compose up -d

# 2. Start React Native development
cd app
npm start

# 3. Alternative: Start with Expo
cd app
npx expo start
```

---

## 🐛 **Troubleshooting**

### **If iPhone can't connect:**

1. **Check firewall** - Ensure Windows firewall allows ports 3000, 3001, 8081
2. **Try different IP** - Use `*************` if `**************` doesn't work
3. **Network verification:**
   ```bash
   # From iPhone browser, test:
   http://**************:3000/health
   ```

### **Firewall Configuration:**
```bash
# Windows PowerShell (Run as Administrator)
New-NetFirewallRule -DisplayName "React Native Metro" -Direction Inbound -Port 8081 -Protocol TCP -Action Allow
New-NetFirewallRule -DisplayName "CCALC Backend" -Direction Inbound -Port 3000 -Protocol TCP -Action Allow
New-NetFirewallRule -DisplayName "CCALC Frontend" -Direction Inbound -Port 3001 -Protocol TCP -Action Allow
```

---

## ✅ **Testing Checklist**

- [ ] Windows services running (docker-compose up)
- [ ] iPhone on same WiFi as Windows PC
- [ ] Firewall configured for ports 3000, 3001, 8081
- [ ] Backend health check works from iPhone browser
- [ ] Expo Go installed on iPhone (Method 1)
- [ ] QR code scannable from Expo dev server

---

## 🎯 **Expected Results**

When working correctly:
1. **Calculator Screen** - Modern iOS calculator interface
2. **BLE Authentication** - Earbud pairing simulation
3. **Chat Interface** - Superuser messaging system
4. **Backend Connection** - Real API calls to Windows backend
5. **Voice Features** - Recording and modulation (when implemented)

---

## 📞 **Support**

If you encounter issues:
1. Check Windows firewall settings
2. Verify iPhone and PC on same network
3. Test backend health endpoint from iPhone Safari
4. Try alternative IP addresses from the list above
